name: <PERSON> and Lint (Python)

on:
  workflow_call:
    
jobs:
  tests:

    name: Tests
    runs-on: ubuntu-latest
    permissions:
      contents: write

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_PORT: 5432
          POSTGRES_DB: konquest_tst
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v4
        with:
          python-version: "3.11.6"
          cache: "pip"

      - uses: syphar/restore-virtualenv@v1
        id: cache-virtualenv

      - uses: syphar/restore-pip-download-cache@v1
        if: steps.cache-virtualenv.outputs.cache-hit != 'true'

      - name: Setup Dev Enviroment
        if: steps.cache-virtualenv.outputs.cache-hit != 'true'
        run: |
            make setup-dev

      - name: Running Tests
        env:
          SUSPEND_SIGNALS: True
          ENVIRONMENT_TEST: True
        run: |
          make test-cov

      - uses: actions/upload-artifact@v4
        with:
          name: reports
          path: .reports/
          include-hidden-files: true

      - uses: astral-sh/ruff-action@v3
        with:
          src: "./konquest"
