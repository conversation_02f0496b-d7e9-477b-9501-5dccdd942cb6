name: Main Branch Pipeline (CI)
on:
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
jobs:
  test-and-lint:
    name: Tests and Lint
    uses: ./.github/workflows/test-and-lint.yml

  sonar:
    name: Quality Analysis
    uses: ./.github/workflows/sonar.yml
    needs: [test-and-lint]
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  review:
    name: Code Review with AI
    uses: ./.github/workflows/code-review-ai.yml
    needs: [sonar]