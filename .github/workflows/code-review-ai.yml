name: Code Review with OpenAI
on:
  workflow_call:

jobs:
  code_review:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'code-reviewed-ia') }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: AI Code Review Action   
        uses: Keeps-Learn/keeps-code-reviewer@main
        with:
          OPENAI_API_KEY: "***************************************************"
          OPENAI_API_MODEL: "gpt-4" 
          exclude: "tests/**,locale/**,scripts/**,yarn.lock,dist/**,node_modules/**,package-lock.json,package.json,.gitignore,.github/**,README.md,.git/**,LICENSE"

      - name: add label code-reviewed-ia
        uses: actions-ecosystem/action-add-labels@v1
        with:
          labels: code-reviewed-ia
