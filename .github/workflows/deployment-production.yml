name: Continuous Deployment (CD) - production

on:
  push:
    branches:
      - main

env:
  DEPLOYMENTS: deployment/konquest  deployment/konquest-worker deployment/konquest-scheduler deployment/konquest-notification-worker deployment/konquest-grpc
  PROJECT_FOLDER: konquest
jobs:
  test-and-lint:
    name: Tests and Lint
    uses: ./.github/workflows/test-and-lint.yml

  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [test-and-lint]
    steps:
    - name: Downloading Source Code
      uses: actions/checkout@v4

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      if: success()
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_EKS }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_EKS }}
        aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

    - name: Login to Amazon ECR Private
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push docker image to Amazon ECR
      env:
        IMAGE_TAG_LATEST: production
        ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}
      run: |
        cd $PROJECT_FOLDER
        docker build -f Dockerfile -t "$ECR_REPOSITORY:$IMAGE_TAG_LATEST" .
        docker push "$ECR_REPOSITORY:$IMAGE_TAG_LATEST"

    - name: Deploy to EKS
      uses: kodermax/kubectl-aws-eks@master
      env:
        KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG }}
        NAMESPACE: production
      with:
        args: rollout restart -n $NAMESPACE $DEPLOYMENTS

  release-on-push:
    runs-on: ubuntu-latest
    needs: [test-and-lint]
    steps:
      - uses: rymndhng/release-on-push-action@master
        with:
          bump_version_scheme: minor
