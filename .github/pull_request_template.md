## Pull Request template
## Description
Please include a summary of the change and which issue is fixed. Please also include relevant motivation and context. List any dependencies that are required for this change.

### Please, go through these steps before you submit a PR.
- [ ] You have a descriptive commit message with a short title (first line).
- [ ] You have only one commit (if not, squash them into one commit).
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published in downstream modules
- [ ] Has added any new dependency
- [ ] You have done your changes in a separate branch. Branches MUST have descriptive names that start with either the `bugfix/` or `feature/` or `hotfix/` prefixes and must have after the slash the id of the task like (DEV-XXX or CU-XXX). Good examples are: `bugfix/DEV-XXX-my-task-title` or `feature/DEV-XXX-my-task-title` or `feature/CU-XXX-my-task-title`.

---------------------------------------------------
## How Has This Been Tested?
Please describe the tests that you ran to verify your changes. Provide instructions so we can reproduce. Please also list any relevant details for your test configuration
- [ ] Test A
- [ ] Test B

## What API’s was affected?
- Example: https://learning-platform-api-stage.keepsdev.com/konquest/missions

## Screenshots (if appropriate):
-  Before this PR
-  After this PR