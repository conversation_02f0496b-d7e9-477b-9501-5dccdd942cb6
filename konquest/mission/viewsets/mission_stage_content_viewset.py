from authentication.keeps_permissions import ALL_PERMISSIONS, MANAGED_MISSION_PERMISSIONS, IsAuthenticatedWithoutXClient
from constants import NEW_MISSION_CONTENT
from custom.keeps_exception_handler import KeepsNoPermissionToEditMission
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from injector import Provider, inject
from mission.models.mission_stage_content import MissionStage, MissionStageContent
from mission.serializers.mission_stage_content_serializer import (
    MissionStageContentListSerializer,
    MissionStageContentSerializer,
)
from mission.services.mission_service import MissionService
from observer.event_manager import EventManager
from rest_framework import status, viewsets
from rest_framework.filters import OrderingFilter
from rest_framework.response import Response
from rules import test_rule
from utils.utils import load_request_user, swagger_safe_queryset


class MissionStageContentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, OrderingFilter)
    filterset_fields = ("stage", "learn_content_uuid")
    ordering_fields = ("order", "updated_date")
    ordering = ("order",)

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(
        self,
        mission_service: MissionService = Provider[MissionService],
        event_service: EventManager = Provider[EventManager],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._mission_service = mission_service
        self.events = event_service

    def get_queryset(self):
        return MissionStageContent.objects.all()

    def get_serializer_class(self):
        return MissionStageContentListSerializer if self.request.method == "GET" else MissionStageContentSerializer

    def create(self, request, *args, **kwargs):
        stage_id = self.request.data.get("stage")
        mission_stage = MissionStage.objects.get(id=stage_id)
        user = load_request_user(request)
        if not test_rule("can_change_mission", user, mission_stage.mission):
            raise KeepsNoPermissionToEditMission()
        self.events.notify(NEW_MISSION_CONTENT, (mission_stage.mission.id,))
        response = super().create(request, *args, **kwargs)
        return response

    def update(self, request, *args, **kwargs):
        content_id = kwargs.get("pk")
        stage_content = MissionStageContent.objects.get(id=content_id)

        user = load_request_user(request)
        if not test_rule("can_change_mission", user, stage_content.stage.mission):
            raise KeepsNoPermissionToEditMission()
        response = super().update(request, *args, **kwargs)
        return response

    def destroy(self, request, *args, **kwargs):
        instance = MissionStageContent.objects.get(id=str(kwargs["pk"]))
        user = load_request_user(request)

        stage_id = str(instance.stage_id)
        mission_id = instance.stage.mission.id

        if not test_rule("can_change_mission", user, instance.stage.mission):
            raise KeepsNoPermissionToEditMission()

        response = super().destroy(request, *args, **kwargs)

        contents = MissionStageContent.objects.filter(stage_id=stage_id).order_by("order")
        for new_order, content in enumerate(contents):
            content.order = new_order + 1
            content.save()

        self.events.notify(NEW_MISSION_CONTENT, (mission_id,))
        return response


class MissionStageIdContentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, OrderingFilter)
    filterset_fields = ("stage", "learn_content_uuid")
    ordering_fields = ("order", "updated_date")
    ordering = ("order",)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    @swagger_safe_queryset(MissionStageContent)
    def get_queryset(self):
        stage_id = str(self.kwargs["stage_id"])
        return MissionStageContent.objects.filter(stage=stage_id)

    def get_serializer_class(self):
        return MissionStageContentListSerializer if self.request.method == "GET" else MissionStageContentSerializer


class MissionContentReorderViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = MANAGED_MISSION_PERMISSIONS

    def update(self, request, *args, **kwargs):
        content_updated = []
        content_error = []

        for content in request.data:
            try:
                instance = MissionStageContent.objects.filter(id=content["content"]).first()

                if instance:
                    instance.order = content["order"]
                    instance.save()
                    content_updated.append({"content_id": content["content"], "order": content["order"]})
                else:
                    content_error.append(
                        {"stage_id": content["content"], "order": content["order"], "error": "content_not_found"}
                    )
            except Exception:
                content_error.append(
                    {"content_id": content["content"], "order": content["order"], "error": "content_not_found"}
                )

        return Response({"updated": content_updated, "error": content_error}, status=status.HTTP_200_OK)
