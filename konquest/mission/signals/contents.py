from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models.signals import post_delete
from django.dispatch import receiver
from learn_content.services.exam_service import ExamService
from mission.models import MissionStageContent
from utils.task_transaction import task_transaction


@receiver(post_delete, sender=MissionStageContent)
def delete_exam(sender, instance: MissionStageContent, using, **kwargs):
    if instance.content_type != "EXAM":
        return

    def run_exam_deletion():
        with task_transaction(delete_exam.__name__, ExamService) as exam_service:
            try:
                exam_service.delete(instance.learn_content_uuid)
            except ObjectDoesNotExist:
                pass

    transaction.on_commit(run_exam_deletion)
