import json
from typing import <PERSON><PERSON>

from celery import shared_task
from config import settings
from confluent_kafka import Consumer, KafkaException
from group.tasks.disabled_group_user_by_workspace import disabled_group_user_by_workspace
from group.tasks.enable_group_user_by_workspace import enable_group_user_by_workspace

ACTIONS_FUNCTIONS = {"c": enable_group_user_by_workspace, "d": disabled_group_user_by_workspace}


class KafkaConsumerUserRoleWorkspaceSingleton:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(KafkaConsumerUserRoleWorkspaceSingleton, cls).__new__(cls, *args, **kwargs)
            cls._instance.config = {
                "bootstrap.servers": settings.KAFKA_SERVERS,
                "group.id": settings.KAFKA_GROUP_ID,
                "auto.offset.reset": "earliest",
                "enable.auto.commit": False,
            }
            cls._instance.consumer = Consumer(cls._instance.config)
            cls._instance.topic_name = settings.TOPIC_NAME_USER_ROLE_WORKSPACE
            cls._instance.consumer.subscribe([cls._instance.topic_name])
        return cls._instance

    def consume(self):
        while True:
            msg = self.consumer.poll(1.0)
            if not msg:
                break
            if msg.error():
                raise KafkaException(msg.error())
            else:
                message = msg.value()
                if message:
                    message = message.decode("utf-8")
                    if self.message_is_valid(message):
                        self.process_message(message)
            self.consumer.commit(msg)

    @staticmethod
    def message_is_valid(message: str) -> bool:
        return message and ('"d"' in message or '"c"' in message)

    @staticmethod
    def process_message(msg):
        message_data = KafkaConsumerUserRoleWorkspaceSingleton.message_to_json(msg)
        op = message_data["payload"]["op"]
        if op in ACTIONS_FUNCTIONS.keys():
            (
                user_id,
                workspace_id,
                role_id,
            ) = KafkaConsumerUserRoleWorkspaceSingleton.get_objects_attributes_from_json_message(message_data)
            if role_id in settings.KONQUEST_ROLES:
                action = ACTIONS_FUNCTIONS[op]
                action.delay(user_id, workspace_id)

    @staticmethod
    def message_to_json(message):
        message = message.replace('"[', "[").replace(']"', "]")
        message = message.replace(chr(92), "").replace(chr(92), "")
        message_data = json.loads(message)
        return message_data

    @staticmethod
    def get_objects_attributes_from_json_message(message_json) -> Tuple[str, str, str]:
        if message_json["payload"]["after"]:
            object_json = message_json["payload"]["after"]
        else:
            object_json = message_json["payload"]["before"]
        user_id = object_json["user_id"]
        workspace_id = object_json["workspace_id"]
        role_id = object_json["role_id"]
        return user_id, workspace_id, role_id


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def consume_user_role_workspace():
    consumer = KafkaConsumerUserRoleWorkspaceSingleton()
    consumer.consume()
