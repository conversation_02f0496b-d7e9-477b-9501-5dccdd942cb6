from unittest.mock import patch

from django.test import TestCase
from kafka_worker.consumer_user_role_workspace import KafkaConsumerUserRoleWorkspaceSingleton

ENABLED_PATH = "kafka_worker.consumer_user_role_workspace.enable_group_user_by_workspace.delay"
DISABLED_PATH = "kafka_worker.consumer_user_role_workspace.disabled_group_user_by_workspace.delay"


class TestConsumerUserRoleWorkspace(TestCase):
    def setUp(self):
        self.consumer = KafkaConsumerUserRoleWorkspaceSingleton()
        self.creation_msg = r"""
        {
        "payload": {
            "before": null,
            "after": {
                "id": "f01534e1-3b44-4a53-bca4-ff34603a1103",
                "status": true,
                "created_date": "2024-03-01T00:33:19.479721Z",
                "updated_date": "2024-03-01T00:33:19.479732Z",
                "workspace_id": "7b2c5110-14d8-4a55-b984-be4eb3b3fdbf",
                "role_id": "a6d23aea-807e-4374-964e-c725b817742d",
                "user_id": "0a405686-92b1-4d62-9cb1-af7223caa4ca",
                "self_sign_up": false
            },
            "op": "c",
            "source": {
                "sequence": [\"8217347103000\",\"8217347123672\"]
                }
        }
        }
        """
        self.deletion_msg = """
        {
        "payload": {
            "before": {
                "id": "e69ebeaa-c856-482b-a0be-20ae58a8865e",
                "status": true,
                "created_date": "2024-02-29T23:54:42.933870d",
                "updated_date": "2024-02-29T23:54:42.933883f",
                "workspace_id": "7b2c5110-14d8-4a55-b984-be4eb3b3fdbg",
                "role_id": "a6d23aea-807e-4374-964e-c725b817742d",
                "user_id": "0a405686-92b1-4d62-9cb1-af7223caa4cj",
                "self_sign_up": false
            },
            "after": null,
            "op": "d"
        }
        }
        """

    @patch("kafka_worker.consumer_user_role_workspace.enable_group_user_by_workspace.delay")
    @patch("kafka_worker.consumer_user_role_workspace.disabled_group_user_by_workspace.delay")
    def test_process_message(self, disabled_group_user_by_workspace, enable_group_user_by_workspace):
        self.consumer.process_message(self.creation_msg)
        self.assertTrue(enable_group_user_by_workspace.called)
        self.consumer.process_message(self.deletion_msg)
        self.assertTrue(disabled_group_user_by_workspace.called)

    def test_message_to_json(self):
        message_json_creation = self.consumer.message_to_json(self.creation_msg)
        message_json_deletion = self.consumer.message_to_json(self.deletion_msg)
        self.assertEqual(message_json_creation["payload"]["op"], "c")
        self.assertEqual(message_json_deletion["payload"]["op"], "d")

    def test_get_objects_attributes_from_json_message(self):
        message_json_creation = self.consumer.message_to_json(self.creation_msg)
        message_json_deletion = self.consumer.message_to_json(self.deletion_msg)
        user_id, workspace_id, role_id = self.consumer.get_objects_attributes_from_json_message(message_json_creation)
        self.assertEqual(user_id, "0a405686-92b1-4d62-9cb1-af7223caa4ca")
        self.assertEqual(workspace_id, "7b2c5110-14d8-4a55-b984-be4eb3b3fdbf")
        self.assertEqual(role_id, "a6d23aea-807e-4374-964e-c725b817742d")
        user_id, workspace_id, role_id = self.consumer.get_objects_attributes_from_json_message(message_json_deletion)
        self.assertEqual(user_id, "0a405686-92b1-4d62-9cb1-af7223caa4cj")
        self.assertEqual(workspace_id, "7b2c5110-14d8-4a55-b984-be4eb3b3fdbg")
        self.assertEqual(role_id, "a6d23aea-807e-4374-964e-c725b817742d")

    def test_not_process_message_if_role_not_is_konquest(self):
        self.creation_msg_not_konquest = self.creation_msg.replace(
            "a6d23aea-807e-4374-964e-c725b817742d", "a6d23aea-007e-4374-964e-c725b817742d"
        )

        with patch(ENABLED_PATH) as enable_group_user_by_workspace:
            self.assertFalse(enable_group_user_by_workspace.called)
        self.deletion_msg_not_konquest = self.deletion_msg.replace(
            "a6d23aea-807e-4374-964e-c725b817742d", "a6d23aea-007e-4374-964e-c725b817742d"
        )
        with patch(DISABLED_PATH) as disabled_group_user_by_workspace:
            self.assertFalse(disabled_group_user_by_workspace.called)

    def test_singleton_pattern(self):
        consumer_2 = KafkaConsumerUserRoleWorkspaceSingleton()
        self.assertEqual(self.consumer, consumer_2)
        self.assertEqual(self.consumer.consumer, consumer_2.consumer)
        self.assertEqual(self.consumer.config, consumer_2.config)
        self.assertEqual(self.consumer.topic_name, consumer_2.topic_name)
        self.assertEqual(id(self.consumer), id(consumer_2))

    def test_message_is_valid(self):
        self.assertTrue(self.consumer.message_is_valid(self.creation_msg))
        self.assertTrue(self.consumer.message_is_valid(self.deletion_msg))
        self.assertFalse(self.consumer.message_is_valid(None))
        self.deletion_msg = self.deletion_msg.replace('"d"', '"p"')
        self.assertFalse(self.consumer.message_is_valid(self.deletion_msg))
