import json
from typing import Sequence

from custom import KeepsClientHeaderNotFoundCompanyError
from custom.keeps_exception_handler import KeepsUnauthorizedError
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from jose import JWSError, jws
from mission.models import Mission
from rest_clients.myaccount import MyAccountClientV2
from rest_framework.permissions import BasePermission
from user_activity.models import MissionEnrollment

USER = "user"
CONTENT = "content"
CURATOR = "curator"
REPORTER = "reporter"
ADMIN = "admin"
SUPER_ADMIN = "super_admin"
INSTRUCTOR = "instructor"
OTHERS = "OTHERS"


class KeepsBasePermission(BasePermission):
    @staticmethod
    def _get_workspace_request(request):
        return request.META.get("HTTP_X_CLIENT") or None

    def get_user_roles(self, token: str, workspace_id: str = None) -> list:
        if not token:
            return []

        try:
            # SonarQube: disable=S5659
            user_id = json.loads(jws.get_unverified_claims(token).decode("utf-8")).get("sub")
        except JWSError:
            return []

        if not user_id:
            return []

        user_info = MyAccountClientV2().get_user_info(token, workspace_id)
        roles = self._load_roles(user_info)

        return roles

    @staticmethod
    def _get_priority_user_role(roles: Sequence[str]) -> str:
        if SUPER_ADMIN in roles:
            return SUPER_ADMIN
        if ADMIN in roles:
            return ADMIN
        if CURATOR in roles:
            return CURATOR
        if CONTENT in roles:
            return CONTENT
        if USER in roles:
            return USER
        if INSTRUCTOR in roles:
            return INSTRUCTOR
        return USER

    def get_priority_user_role_by_token(self, token: str, workspace_id: str = None) -> str:
        user_roles = self.get_user_roles(token, workspace_id)
        return self._get_priority_user_role(user_roles)

    def get_priority_user_role_for_sync_missions(self, token: str, workspace_id: str = None) -> str:
        """
        Use this function for get the priority role at the logged user when used some sync mission API
        """
        user_roles = self.get_user_roles(token, workspace_id)
        priority_role = self._get_priority_user_role(user_roles)
        if INSTRUCTOR in user_roles and priority_role == USER:
            return INSTRUCTOR
        return priority_role

    def _check_role(self, request, role):
        user = request.user

        if not user or isinstance(user, AnonymousUser):
            raise KeepsUnauthorizedError()

        workspace_id = user.get("client_id")

        if not workspace_id:
            raise KeepsClientHeaderNotFoundCompanyError()

        user_roles = self.get_user_roles(request.META.get("HTTP_AUTHORIZATION"), user["client_id"])
        request.user["role"] = self._get_priority_user_role(user_roles)
        request.user["managed"] = request.user["role"] in MANAGER_ROLES
        return role in user_roles

    @staticmethod
    def _load_roles(user_info: dict) -> list:
        roles = [role["key"] for role in user_info["roles"] if role["application_id"] == settings.APPLICATION_ID]
        return roles


class IsAuthenticatedWithoutXClient(KeepsBasePermission):
    def has_permission(self, request, view):
        return bool(request.user is not None)


class KeepsIntegrationPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        if (
            "token_integration" in request.user
            and request.user["token_integration"] == settings.KEEPS_SECRET_TOKEN_INTEGRATION
        ):
            return True

        return self._check_role(request, USER)


class KeepsUserPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, USER)


class KeepsInstructorPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, INSTRUCTOR)


class KeepsContentPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, CONTENT)


class KeepsCuratorPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, CURATOR)


class KeepsReporterPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, REPORTER)


class KeepsPlatformAdminPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, ADMIN)


class KeepsSuperAdminPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        return self._check_role(request, SUPER_ADMIN)


class KeepsContributorMissionPermission(KeepsBasePermission):
    def has_permission(self, request, view):
        args_url = request.parser_context.get("kwargs")
        pk = args_url.get("mission_id") or args_url.get("pk")
        mission = Mission.objects.filter(pk=pk).first()

        if mission is None:
            try:
                mission_enrollment = MissionEnrollment.objects.get(pk=pk)
                mission = mission_enrollment.mission
            except MissionEnrollment.DoesNotExist:
                return False

        user_id = request.user.get("sub")
        return mission.contributors.filter(user_id=user_id).exists()


ADMIN_PERMISSIONS = (KeepsSuperAdminPermission | KeepsPlatformAdminPermission,)

MANAGED_PERMISSIONS = (
    KeepsSuperAdminPermission
    | KeepsPlatformAdminPermission
    | KeepsCuratorPermission
    | KeepsContentPermission
    | KeepsInstructorPermission,
)

MANAGED_MISSION_PERMISSIONS = (
    KeepsSuperAdminPermission
    | KeepsPlatformAdminPermission
    | KeepsCuratorPermission
    | KeepsContentPermission
    | KeepsInstructorPermission
    | KeepsContributorMissionPermission,
)

ALL_PERMISSIONS = (
    KeepsSuperAdminPermission
    | KeepsPlatformAdminPermission
    | KeepsCuratorPermission
    | KeepsContentPermission
    | KeepsUserPermission
    | KeepsInstructorPermission,
)

ADMIN_ROLES = (ADMIN, SUPER_ADMIN)
MANAGER_ROLES = (ADMIN, SUPER_ADMIN, CONTENT, CURATOR)
