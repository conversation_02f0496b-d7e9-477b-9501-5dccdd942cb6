# Generated by Django 2.2 on 2022-09-08 12:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("achievement", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="achievement",
            name="deleted",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="achievement",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="achievementclass",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="achievementclass",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="achievementtype",
            name="deleted",
            field=models.<PERSON><PERSON>anField(default=False, verbose_name="Deleted"),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="achievementtype",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
    ]
