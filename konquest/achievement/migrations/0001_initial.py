# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("account", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AchievementClass",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
            ],
            options={
                "verbose_name_plural": "Achievement Class",
                "db_table": "achievement_class",
            },
        ),
        migrations.CreateModel(
            name="AchievementType",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
                ("image", models.URLField(blank=True, null=True, verbose_name="Image")),
                ("value", models.PositiveIntegerField(verbose_name="Value")),
                (
                    "achievement_class",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="achievement.AchievementClass",
                        verbose_name="Achievement Class",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Achievement Types",
                "db_table": "achievement_type",
            },
        ),
        migrations.CreateModel(
            name="Achievement",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "achievement_class",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="achievement.AchievementClass",
                        verbose_name="Achievement Class",
                    ),
                ),
                (
                    "achievement_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="achievement.AchievementType",
                        verbose_name="Achievement Type",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="account.User", verbose_name="User"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "User Achievements",
                "db_table": "achievement",
            },
        ),
    ]
