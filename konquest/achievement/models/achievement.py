import uuid

from account.models import User
from achievement.models.achievement_class import AchievementClass
from achievement.models.achievement_type import AchievementType
from django.db import models
from utils.models import BaseModel


class Achievement(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    achievement_class = models.ForeignKey(AchievementClass, verbose_name="Achievement Class", on_delete=models.PROTECT)
    achievement_type = models.ForeignKey(AchievementType, verbose_name="Achievement Type", on_delete=models.PROTECT)

    user = models.ForeignKey(User, verbose_name="User", on_delete=models.PROTECT)

    class Meta:
        verbose_name_plural = "User Achievements"
        db_table = "achievement"
