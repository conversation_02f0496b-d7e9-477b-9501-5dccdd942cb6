import uuid

from achievement.models.achievement_class import AchievementClass
from django.db import models
from utils.models import BaseModel


class AchievementType(BaseModel):
    """
    Setting achievement variations types by class

    Samples:
    - finish mission
      10 finished missions, 20 finished missions, ...
    - create mission
      5 created missions, 10 created missions, ...
    - hours in mission
      50 hours, 100 hours, ..
    - days in mission
      20 days in mission, 50 days in missions, ...
    - answer question
      100 answered questions, 200 answered questions, ...
    - goal hit
      5 goals reached, 10 goals reached, ...
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    image = models.URLField(verbose_name="Image", null=True, blank=True)
    value = models.PositiveIntegerField(verbose_name="Value")

    achievement_class = models.ForeignKey(AchievementClass, verbose_name="Achievement Class", on_delete=models.PROTECT)

    class Meta:
        verbose_name_plural = "Achievement Types"
        db_table = "achievement_type"
