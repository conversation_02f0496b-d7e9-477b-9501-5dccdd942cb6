# -*- coding: utf-8 -*-

from achievement.models import AchievementClass
from achievement.serializers.achievement_class_serializer import AchievementClassSerializer
from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter


class AchievementClassViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = (
        "name",
        "description",
    )
    ordering_fields = ("name", "created_date", "updated_date")
    ordering = ("updated_date",)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return AchievementClass.objects.all()

    def get_serializer_class(self):
        return AchievementClassSerializer
