# -*- coding: utf-8 -*-

from achievement.models import AchievementType
from achievement.serializers.achievement_type_serializer import AchievementTypeSerializer
from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter


class AchievementTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("value", "achievement_class")
    search_fields = (
        "name",
        "description",
    )
    ordering_fields = ("name", "value", "created_date", "updated_date")
    ordering = ("updated_date",)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return AchievementType.objects.all()

    def get_serializer_class(self):
        return AchievementTypeSerializer
