# -*- coding: utf-8 -*-

from achievement.models import Achievement
from achievement.serializers.achievement_serializer import AchievementSerializer
from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, SearchFilter
from utils.utils import swagger_safe_queryset


class AchievementViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("achievement_type", "user")
    search_fields = ("type__name", "user__name")
    ordering_fields = ("created_date", "updated_date")
    ordering = ("updated_date",)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    @swagger_safe_queryset(Achievement)
    def get_queryset(self):
        user_id = self.request.user["sub"] if self.request.user else None
        return Achievement.objects.filter(user_id=user_id)

    def get_serializer_class(self):
        return AchievementSerializer
