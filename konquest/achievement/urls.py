from achievement.views.achievement_class_viewset import AchievementClassViewSet
from achievement.views.achievement_type_viewset import AchievementTypeViewSet
from achievement.views.achievement_viewset import AchievementViewSet
from django.urls import path

_READ_ONLY = {"get": "list"}

_READ_ONLY_DETAIL = {"get": "retrieve"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("", AchievementViewSet.as_view(_LIST), name="achievements-list"),
    path("/<uuid:pk>", AchievementViewSet.as_view(_DETAIL), name="achievements-detail"),
    path("/classes", AchievementClassViewSet.as_view(_READ_ONLY), name="achievements-classes-list"),
    path("/classes/<uuid:pk>", AchievementClassViewSet.as_view(_READ_ONLY_DETAIL), name="achievement-classes-detail"),
    path("/types", AchievementTypeViewSet.as_view(_READ_ONLY), name="achievement-types-list"),
    path("/types/<uuid:pk>", AchievementTypeViewSet.as_view(_READ_ONLY_DETAIL), name="achievement-types-detail"),
]
