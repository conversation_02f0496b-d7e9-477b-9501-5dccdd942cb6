from django.utils.translation import gettext_noop

AN_UNEXPECTED_ERROR_HAS_OCCURRED = gettext_noop("an_unexpected_error_has_occurred")
USER_NOT_FOUND = gettext_noop("user_not_found")
MISSION_NOT_FOUND = gettext_noop("mission_not_found")
GROUP_NOT_FOUND = gettext_noop("group_not_found")
USER_ALREADY_LINKED = gettext_noop("user_already_linked")
UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION = gettext_noop("unable_to_enroll_in_an_in_inactive_mission")
