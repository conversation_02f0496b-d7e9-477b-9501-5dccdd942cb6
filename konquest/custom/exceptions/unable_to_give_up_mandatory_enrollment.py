from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation.trans_null import gettext_noop

UNABLE_TO_GIVE_UP_MANDATORY_ENROLLMENT = gettext_noop("unable_to_give_up_mandatory_enrollment")


class UnableToGiveUpMandatoryEnrollment(KeepsServiceError):
    def __init__(self):
        super().__init__(UNABLE_TO_GIVE_UP_MANDATORY_ENROLLMENT, _(UNABLE_TO_GIVE_UP_MANDATORY_ENROLLMENT))
