from custom.exceptions.keeps_permission_error import KeepsPermissionError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

NOT_ALLOWED_SEND_CERTIFICATE_TO_INTEGRATED_MISSION = gettext_noop("not_allowed_send_certificate_to_integrated_mission")


class NotAllowedSendCertificateIntegratedMission(KeepsPermissionError):
    def __init__(self):
        super().__init__(_(NOT_ALLOWED_SEND_CERTIFICATE_TO_INTEGRATED_MISSION))
