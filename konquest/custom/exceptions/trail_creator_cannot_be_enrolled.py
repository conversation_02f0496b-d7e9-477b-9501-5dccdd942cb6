from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

TRAIL_CREATOR_CANNOT_BE_ENROLLED = gettext_noop("trail_creator_cannot_be_enrolled")


class TrailCreatorCannotBeEnrolled(KeepsServiceError):
    def __init__(self):
        super().__init__(TRAIL_CREATOR_CANNOT_BE_ENROLLED, _(TRAIL_CREATOR_CANNOT_BE_ENROLLED))
