from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

UNABLE_TO_PUBLISH_INCOMPLETE_PRESENTIAL_MISSION = gettext_noop("unable_to_publish_incomplete_presential_mission")


class UnableToPublishIncompletePresentialMission(KeepsServiceError):
    def __init__(self):
        super().__init__(
            UNABLE_TO_PUBLISH_INCOMPLETE_PRESENTIAL_MISSION, _(UNABLE_TO_PUBLISH_INCOMPLETE_PRESENTIAL_MISSION)
        )
