from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

USER_IS_NOT_LIKED_TO_ANY_TRAIL_GROUP = gettext_noop("user_is_not_linked_to_any_trail_group")


class UserIsNotLinkedToAnyTrailGroup(KeepsServiceError):
    def __init__(self):
        super().__init__(USER_IS_NOT_LIKED_TO_ANY_TRAIL_GROUP, _(USER_IS_NOT_LIKED_TO_ANY_TRAIL_GROUP))
