from custom.exceptions.keeps_permission_error import KeepsPermissionError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

USER_NOT_ALLOWED_TO_ACCESS_MISSION = gettext_noop("user_not_allowed_to_access_mission")


class NotAllowedToAccessMission(KeepsPermissionError):
    def __init__(self):
        super().__init__(_(USER_NOT_ALLOWED_TO_ACCESS_MISSION))
