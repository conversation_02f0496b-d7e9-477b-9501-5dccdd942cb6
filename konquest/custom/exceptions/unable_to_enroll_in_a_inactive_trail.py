from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

UNABLE_TO_ENROLL_IN_A_INACTIVE_TRAIL = gettext_noop("unable_to_enroll_in_a_inactive_trail")


class UnableToEnrollInAInactiveTrail(KeepsServiceError):
    def __init__(self):
        super().__init__(UNABLE_TO_ENROLL_IN_A_INACTIVE_TRAIL, _(UNABLE_TO_ENROLL_IN_A_INACTIVE_TRAIL))
