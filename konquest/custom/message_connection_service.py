import json
from typing import Optional

import pika
from custom.discord_webhook import Disco<PERSON><PERSON><PERSON><PERSON>ok<PERSON>ogger
from pika import BlockingConnection
from pika.adapters.blocking_connection import BlockingChannel
from pika.exceptions import AMQPConnectionError, ChannelWrongStateError


class MessageConnectionService:
    connection: Optional[BlockingConnection]

    def __init__(
        self,
        message_broker_url: str,
        default_routing_key: str,
        exchange: str,
        exchange_type: str,
    ):
        self.channel: Optional[BlockingChannel] = None
        self._exchange = exchange
        self._exchange_type = exchange_type
        self._default_routing_key = default_routing_key
        self._message_broker_url = message_broker_url
        self._queue_is_durable = True
        self.connection = None
        self.logger = DiscordWebhookLogger()

    def connect(self):
        try:
            self.connection = pika.BlockingConnection(pika.URLParameters(self._message_broker_url))
            self.channel = self.connection.channel()
            self._declare_exchange_and_queue()
        except AMQPConnectionError:
            self.connection = None
            self.channel = None

    def ensure_active_channel(self):
        if not self.connection or self.connection.is_closed or not self.channel or self.channel.is_closed:
            self.connect()

    def send(self, payload: dict, task_name: str = None, routing_key: str = None):
        self.ensure_active_channel()
        try:
            self._publish_message(payload, task_name, routing_key)
        except (ChannelWrongStateError, AMQPConnectionError):
            self.connect()
            if self.channel:
                self._publish_message(payload, task_name, routing_key)
                return
            self.logger.emit_short_message("MessageConnectionService.send", "Connection to rabbitmq failed")

    def _declare_exchange_and_queue(self):
        self.channel.exchange_declare(
            exchange=self._exchange, exchange_type=self._exchange_type, durable=self._queue_is_durable
        )
        self.channel.queue_bind(
            exchange=self._exchange,
            queue=self._default_routing_key,
            routing_key=self._default_routing_key,
        )

    def _publish_message(self, payload: dict, task_name: str = None, routing_key: str = None):
        if not routing_key:
            routing_key = self._default_routing_key
        message_body = json.dumps(payload, default=str).encode("utf8")
        self.channel.basic_publish(
            exchange=self._exchange,
            routing_key=routing_key,
            body=message_body,
            properties=pika.spec.BasicProperties(headers={"task_name": task_name}),
        )
