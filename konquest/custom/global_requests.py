from threading import current_thread
from typing import Optional

_REQUESTS = {}


def set_request(thread_id, request):
    _REQUESTS[thread_id] = request


def cleanup_request(thread_id):
    del _REQUESTS[thread_id]


def get_current_request():
    """
    Returns the current request (or None)
    """
    thread_id = current_thread().ident
    return _REQUESTS.get(thread_id, None)


def get_current_user() -> Optional[dict]:
    """
    Returns the current user (or None) extracted from the current request.
    """
    current_request = get_current_request()
    if current_request and current_request.user:
        return current_request.user
