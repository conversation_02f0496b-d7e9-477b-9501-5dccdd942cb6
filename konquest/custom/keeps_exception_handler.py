from custom.exceptions.keeps_permission_error import KeepsPermissionError
from django.core.exceptions import ObjectDoesNotExist
from django.core.exceptions import ValidationError as DjangoValidationError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from rest_framework import status
from rest_framework.exceptions import APIException, ValidationError

NOT_PERMISSION_TO_MODIFY_THIS_MISSION = gettext_noop("not_permission_to_modify_this_mission")
EVALUATION_NOT_REQUIRED_FOR_THIS_MISSION = gettext_noop("evaluation_not_required_for_this_mission")
NOT_PERMISSION_TO_MODIFY_THIS_MISSION_ENROLLMENT = gettext_noop("not_permission_to_modify_this_mission_enrollment")
NOT_PERMISSION_TO_MODIFY_THIS_CHANNEL = gettext_noop("not_permission_to_modify_this_channel")
ENROLLMENT_LINKED_IN_TRAIL = gettext_noop("enrollment_linked_in_trail")
NOT_ENOUGH_SEATS_FOR_ENROLLMENTS = gettext_noop("not_enough_seats_for_enrollments")
SEATS_CANNOT_BE_LESS_THAN_THE_NUMBER_OF_REMAINING_SEATS = gettext_noop(
    "seats_cannot_be_less_than_the_number_of_remaining_seats"
)
NOT_PERMISSION_TO_DELETE_CHANNEL_COMMENT = gettext_noop("not_permission_to_modify_channel_comment")
NOT_PERMISSION_TO_TRACK_MISSION_ENROLLMENT = gettext_noop("not_permission_to_track_mission_enrollment")
USER_DOESNT_HAVE_INSTRUCTOR_ROLES = gettext_noop("user_doesnt_have_instructor_roles")
INVALID_SYNC_MISSION_MODEL = gettext_noop("invalid_sync_mission_model")
NEW_USER_CREATOR_DOESNT_HAVE_MANAGER_PERMISSION = gettext_noop("new_user_creator_doesnt_have_manager_permission")
NEW_USER_CREATOR_IS_INACTIVE = gettext_noop("new_user_creator_is_inactive")
NOT_PERMISSION_TO_RENEW_ENROLLMENT = gettext_noop("not_permission_to_renew_enrollment")
NOT_PERMISSION_TO_TRANSFER_THIS_MISSION = gettext_noop("not_permission_to_transfer_this_mission")
NOT_PERMISSION_TO_TRANSFER_TO_TARGET_WORKSPACE = gettext_noop("not_permission_to_transfer_to_target_workspace")
NOT_PERMISSION_TO_ROLLBACK_MISSION_TRANSACTION = gettext_noop("not_permission_to_rollback_mission_transaction")
NOT_PERMISSION_TO_DUPLICATE_LIVE_PRESENTIAL = gettext_noop("not_permission_to_duplicate_live_presential")
PAGE_NOT_FOUND = gettext_noop("page_not_found")
UNABLE_TO_PUBLISH_MISSION_WITH_OLD_PERIODS = gettext_noop("unable_to_publish_mission_with_old_periods")
NOT_PERMISSION_TO_DELETE_CATEGORY_LINKED_TO_A_CHANNEL_OR_MISSION = gettext_noop(
    "not_permission_to_delete_category_linked_to_a_channel_or_mission"
)
MISSION_ALREADY_LINKED_IN_WORKSPACE = gettext_noop("mission_already_linked_in_workspace")
UNABLE_TO_RESTART_ENROLLED_ENROLLMENT = gettext_noop("unable_to_restart_enrolled_enrollment")
UNABLE_TO_RESTART_STARTED_ENROLLMENT = gettext_noop("unable_to_restart_started_enrollment")
UNABLE_TO_RESTART_ENROLLMENT = gettext_noop("unable_to_restart_enrollment")
NOT_PERMISSION_TO_TRANSFER_THIS_LEARNING_TRAIL = gettext_noop("not_permission_to_transfer_this_learning_trail")
NOT_PERMISSION_TO_RENEW_ENROLLMENT_BLOCKED_BY_WORKSPACE = gettext_noop(
    "not_permission_to_renew_enrollment_blocked_by_workspace"
)


class KeepsError(APIException):
    def __init__(self, detail, i18n, status_code):
        super().__init__()
        self.i18n = i18n
        self.detail = detail
        self.status_code = status_code


class PulseNotFound(ObjectDoesNotExist):
    def __init__(self):
        super().__init__("Pulse not Found")


class KeepsRuntimeError(Exception):
    def __init__(self, msg, description=""):
        self.msg = msg
        self.description = description
        super().__init__(msg, description)


class KeepsEnrollServiceCalledWithInvalidParamsError(KeepsRuntimeError):
    def __init__(self, description=None):
        message = "enrollment_service_called_with_invalid_params"
        if not description:
            description = (
                "No action user (action_user) provided. Please provide an action user for the batch or "
                "indicate if it was triggered by an internal service (triggered_by_internal_service)."
            )
        super().__init__(message, description)


class KeepsServiceError(Exception):
    def __init__(self, msg, description=""):
        self.msg = msg
        self.description = description
        super().__init__(msg, description)


class KeepsEnrollmentLinkedInTrail(KeepsServiceError):
    def __init__(self):
        super().__init__(ENROLLMENT_LINKED_IN_TRAIL, _(ENROLLMENT_LINKED_IN_TRAIL))


class KeepsMissionAlreadyLinkedInWorkspace(KeepsServiceError):
    def __init__(self):
        super().__init__(MISSION_ALREADY_LINKED_IN_WORKSPACE, _(MISSION_ALREADY_LINKED_IN_WORKSPACE))


class KPUnableToPublishMissionWithOldPeriods(KeepsServiceError):
    def __init__(self):
        super().__init__(UNABLE_TO_PUBLISH_MISSION_WITH_OLD_PERIODS, _(UNABLE_TO_PUBLISH_MISSION_WITH_OLD_PERIODS))


class KeepsNoPermissionToEditMission(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_MODIFY_THIS_MISSION)
        super().__init__(self.message)


class KeepsNoPermissionToTransferMission(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_TRANSFER_THIS_MISSION)
        super().__init__(self.message)


class KeepsNoPermissionToRollbackMissionTransaction(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_ROLLBACK_MISSION_TRANSACTION)
        super().__init__(self.message)


class KeepsNoPermissionToTransferMissionToTargetWorkspace(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_TRANSFER_TO_TARGET_WORKSPACE)
        super().__init__(self.message)


class KeepsNoPermissionToTransferOwnerLearningTrail(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_TRANSFER_THIS_LEARNING_TRAIL)
        super().__init__(self.message)


class KeepsNoPermissionToRenewEnrollment(KeepsPermissionError):
    def __init__(self, message: str = None) -> None:
        if not message:
            message = NOT_PERMISSION_TO_RENEW_ENROLLMENT
        super().__init__(_(message))


class KeepsNoPermissionToDeleteChannelComment(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_DELETE_CHANNEL_COMMENT)
        super().__init__(self.message)


class KeepsEvaluationMissionNotRequired(KeepsPermissionError):
    def __init__(self):
        self.message = _(EVALUATION_NOT_REQUIRED_FOR_THIS_MISSION)
        super().__init__(self.message)


class KPNotEnoughSeatsForEnrollments(KeepsServiceError):
    def __init__(self):
        super().__init__(NOT_ENOUGH_SEATS_FOR_ENROLLMENTS, _(NOT_ENOUGH_SEATS_FOR_ENROLLMENTS))


class KPSeatsCannotBeLessThanTheNumberOfRemainingSeats(KeepsServiceError):
    def __init__(self):
        super().__init__(
            SEATS_CANNOT_BE_LESS_THAN_THE_NUMBER_OF_REMAINING_SEATS,
            _(SEATS_CANNOT_BE_LESS_THAN_THE_NUMBER_OF_REMAINING_SEATS),
        )


class KPNotAllowedDeleteCategoryLinkedToAChannelOrMission(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_DELETE_CATEGORY_LINKED_TO_A_CHANNEL_OR_MISSION)
        super().__init__(self.message)


class KeepsNoPermissionToEditMissionEnrollment(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_MODIFY_THIS_MISSION_ENROLLMENT)
        super().__init__(self.message)


class KeepsNoPermissionToEditLearningTrailEnrollment(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_MODIFY_THIS_MISSION_ENROLLMENT)
        super().__init__(self.message)


class ChannelEditNotAuthorizedException(KeepsPermissionError):
    def __init__(self):
        self.message = NOT_PERMISSION_TO_MODIFY_THIS_CHANNEL
        super().__init__(self.message)


class KeepsNoPermissionToTrackMissionEnrollment(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_TRACK_MISSION_ENROLLMENT)
        super().__init__(self.message)


class KeepsBadRequestError(KeepsError):
    def __init__(self, detail=None, i18n=None):
        if not detail:
            detail = _(i18n)
        super().__init__(detail, i18n, status.HTTP_400_BAD_REQUEST)


class KeepsNotFoundError(KeepsError):
    def __init__(self, detail, i18n):
        super().__init__(detail, i18n, status.HTTP_404_NOT_FOUND)


class KeepsNotAllowedError(KeepsError):
    def __init__(self, detail, i18n):
        super().__init__(detail, i18n, status.HTTP_403_FORBIDDEN)


class KeepsUnauthorizedError(KeepsError):
    def __init__(self):
        super().__init__("Unauthorized", "unauthorized", status.HTTP_401_UNAUTHORIZED)


class KeepsClientHeaderNotFoundCompanyError(KeepsError):
    def __init__(self):
        super().__init__(
            "x_client_not_found", "Workspace not found (set x-client on header)", status.HTTP_403_FORBIDDEN
        )


class KeepsNotFoundCompanyError(KeepsError):
    def __init__(self):
        super().__init__(
            "company_not_found", "Workspace not found, check if Konquest service is active", status.HTTP_403_FORBIDDEN
        )


class KeepsNotAllowedAccessCompanyError(KeepsError):
    def __init__(self):
        super().__init__(
            "You do not have permission to access data for this company",
            "not_allowed_access_company_resources",
            status.HTTP_403_FORBIDDEN,
        )


class KeepsNotAllowedToManagedMission(KeepsNotAllowedError):
    def __init__(self):
        super().__init__(
            i18n="not_permission_to_modify_this_mission",
            detail="You don't have permission to modify this mission or the mission not exists",
        )


class NotAllowedChangeThisMissionStatus(KeepsNotAllowedError):
    def __init__(self):
        super().__init__(
            i18n="not_permission_to_change_this_mission_status",
            detail="You don't have permission to change this mission status",
        )


class NotAllowedDuplicateThisMission(KeepsNotAllowedError):
    def __init__(self):
        super().__init__(
            i18n="not_permission_to_duplicate_this_mission",
            detail="You don't have permission to duplicate this mission",
        )


class NotAllowedShareThisMission(KeepsNotAllowedError):
    def __init__(self):
        super().__init__(
            i18n="not_permission_to_share_this_mission",
            detail="You don't have permission to share this mission",
        )


class NotAllowedTransferThisMission(KeepsNotAllowedError):
    def __init__(self):
        super().__init__(
            i18n="not_permission_to_transfer_this_mission",
            detail="You don't have permission to transfer this mission",
        )


class NotAllowedAddOrRemoveThisGroupFromMission(KeepsNotAllowedError):
    def __init__(self):
        super().__init__(
            i18n="not_permission_add_or_remove_this_group_from_mission",
            detail="You don't have permission to add or remove this group from mission",
        )


class KeepsUserDoesntHaveInstructorRoles(KeepsServiceError):
    def __init__(self):
        super().__init__(USER_DOESNT_HAVE_INSTRUCTOR_ROLES, _(USER_DOESNT_HAVE_INSTRUCTOR_ROLES))


class KeepsInvalidSyncMissionModel(KeepsServiceError):
    def __init__(self):
        super().__init__(INVALID_SYNC_MISSION_MODEL, _(INVALID_SYNC_MISSION_MODEL))


class KeepsUserDoesntHaveManagerPermission(KeepsServiceError):
    def __init__(self):
        super().__init__(
            NEW_USER_CREATOR_DOESNT_HAVE_MANAGER_PERMISSION, _(NEW_USER_CREATOR_DOESNT_HAVE_MANAGER_PERMISSION)
        )


class KeepsUserCreatorIsInactive(KeepsServiceError):
    def __init__(self):
        super().__init__(NEW_USER_CREATOR_IS_INACTIVE, _(NEW_USER_CREATOR_IS_INACTIVE))


class KeepsNoPermissionToDuplicateMissionLivePresential(KeepsPermissionError):
    def __init__(self):
        self.message = _(NOT_PERMISSION_TO_DUPLICATE_LIVE_PRESENTIAL)
        super().__init__(self.message)


class KeepsMissionCoverUnsupportedMediaTypeError(APIException):
    def __init__(self):
        super().__init__()
        self.i18n = "mission_cover_unsupported_media_type"
        self.detail = "Image not allowed. Use PNG, WEBP or JPG"
        self.status_code = 415


class UnableToRestartEnrollmentInvalidStatus(KeepsServiceError):
    def __init__(self, status):
        error_message_by_status = {
            "STARTED": UNABLE_TO_RESTART_STARTED_ENROLLMENT,
            "ENROLLED": UNABLE_TO_RESTART_ENROLLED_ENROLLMENT,
            "OTHERS": UNABLE_TO_RESTART_ENROLLMENT,
        }
        if status not in error_message_by_status:
            status = "OTHERS"
        error_message = error_message_by_status[status]
        super().__init__(error_message, _(error_message))


def transform_exception(exception):
    if not isinstance(exception, DjangoValidationError):
        return exception
    detail = _get_django_validation_detail_error(exception)
    return ValidationError(detail)


def _get_django_validation_detail_error(exception) -> str:
    if hasattr(exception, "message_dict"):
        return exception.message_dict
    if hasattr(exception, "messages"):
        return exception.messages
    if hasattr(exception, "error_list"):
        return exception.error_list
    if hasattr(exception, "message"):
        return exception.message

    return "Validation Error"
