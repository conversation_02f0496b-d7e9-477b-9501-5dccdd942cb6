from threading import current_thread

from custom.global_requests import cleanup_request, set_request


class GlobalRequestMiddleware(object):
    """
    Middleware that stores the current request to be used from any part of the code.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        thread_id = current_thread().ident
        set_request(thread_id, request)

        # call the next middleware/view
        response = self.get_response(request)

        cleanup_request(thread_id)
        return response
