import uuid

from custom.global_requests import get_current_user
from custom.middlewares.global_requests_middleware import GlobalRequestMiddleware
from django.test import TestCase

REQUEST_USER = {"id": uuid.uuid4()}


class StubRequest:
    def __init__(self, user):
        self.user = user


class NextMiddleware:
    def __call__(self, request):
        assert get_current_user() == REQUEST_USER


class TestGlobalRequestsMiddleware(TestCase):
    def test_should_set_current_request_and_be_possible_get_current_user(self):
        next_middleware = NextMiddleware()
        middleware = GlobalRequestMiddleware(next_middleware)

        middleware(StubRequest(REQUEST_USER))

        # threads store must be clean after response returned
        self.assertEqual(get_current_user(), None)
