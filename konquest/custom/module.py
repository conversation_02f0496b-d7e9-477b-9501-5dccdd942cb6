from config import settings
from custom.discord_webhook import Discord<PERSON>ebhookLogger
from custom.notification_connection_service import NotificationConnectionService
from custom.regulatory_compliance_connection_service import RegulatoryComplianceConnectionService
from injector import Module, provider, singleton


class CustomModule(Module):
    @singleton
    @provider
    def discord_webhook(self) -> DiscordWebhookLogger:
        return DiscordWebhookLogger()

    @singleton
    @provider
    def regulatory_compliance_connection_service(self) -> RegulatoryComplianceConnectionService:
        return RegulatoryComplianceConnectionService(
            settings.BROKER_URL,
            settings.REGULATORY_COMPLIANCE_QUEUE,
            settings.REGULATORY_COMPLIANCE_EXCHANGE,
            settings.REGULATORY_COMPLIANCE_EXCHANGE_TYPE,
        )

    @singleton
    @provider
    def notification_connection_service(self) -> NotificationConnectionService:
        return NotificationConnectionService(
            settings.BROKER_URL,
            settings.NOTIFICATION_MESSAGE_QUEUE,
            settings.NOTIFICATION_EXCHANGE,
            settings.NOTIFICATION_EXCHANGE_TYPE,
        )
