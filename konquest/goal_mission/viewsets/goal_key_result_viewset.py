# -*- coding: utf-8 -*-

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from goal_mission.models.goal_key_result import GoalKeyResult
from goal_mission.serializers.goal_key_result_serializer import GoalKeyResultSerializer
from rest_framework import viewsets
from rest_framework.filters import OrderingFilter, SearchFilter


class GoalKeyResultViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return GoalKeyResult.objects.all()

    def get_serializer_class(self):
        return GoalKeyResultSerializer
