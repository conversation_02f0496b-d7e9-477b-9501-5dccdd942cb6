# -*- coding: utf-8 -*-

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from goal_mission.models.mission_goal import MissionGoal
from goal_mission.serializers.mission_goal_serializer import MissionGoalSerializer
from rest_framework import viewsets
from rest_framework.filters import OrderingFilter, SearchFilter


class MissionGoalViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return MissionGoal.objects.all()

    def get_serializer_class(self):
        return MissionGoalSerializer
