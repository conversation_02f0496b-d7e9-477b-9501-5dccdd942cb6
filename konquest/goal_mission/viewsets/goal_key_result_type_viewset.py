# -*- coding: utf-8 -*-

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from goal_mission.models.goal_key_result_type import GoalKeyResultType
from goal_mission.serializers.goal_key_result_type_serializer import GoalKeyResultTypeSerializer
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter


class GoalKeyResultTypeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return GoalKeyResultType.objects.all()

    def get_serializer_class(self):
        return GoalKeyResultTypeSerializer
