import uuid

from django.db import models
from goal_mission.models.goal_type import GoalType
from mission.models.mission import Mission
from utils.models import BaseModel


class MissionGoal(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    mission = models.ForeignKey(Mission, verbose_name="Mission", on_delete=models.PROTECT)

    goal_type = models.ForeignKey(GoalType, verbose_name="Goal Type", on_delete=models.PROTECT)

    class Meta:
        verbose_name_plural = "Mission Goal"
        db_table = "mission_goal"
