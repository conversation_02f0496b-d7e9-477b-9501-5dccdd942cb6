import uuid

from django.db import models
from goal_mission.models.goal_type import GoalType
from utils.models import BaseModel


class GoalKeyResultType(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)

    goal_type = models.ForeignKey(GoalType, verbose_name="Goal Type", on_delete=models.PROTECT)

    class Meta:
        verbose_name_plural = "Goal Key Result Type"
        db_table = "goal_key_result_type"
