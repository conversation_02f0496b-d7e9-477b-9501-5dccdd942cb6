# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="GoalKeyResult",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
                ("initial_value", models.FloatField(verbose_name="Initial Value")),
                ("goal_value", models.FloatField(verbose_name="Goal Value")),
                ("goal_hit", models.CharField(max_length=100, verbose_name="Goal Hit")),
            ],
            options={
                "verbose_name_plural": "Goal Key Result",
                "db_table": "goal_key_result",
            },
        ),
        migrations.CreateModel(
            name="GoalKeyResultType",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
            ],
            options={
                "verbose_name_plural": "Goal Key Result Type",
                "db_table": "goal_key_result_type",
            },
        ),
        migrations.CreateModel(
            name="GoalType",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
            ],
            options={
                "verbose_name_plural": "Goal Types",
                "db_table": "goal_type",
            },
        ),
        migrations.CreateModel(
            name="MissionGoal",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "goal_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="goal_mission.GoalType",
                        verbose_name="Goal Type",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Mission Goal",
                "db_table": "mission_goal",
            },
        ),
    ]
