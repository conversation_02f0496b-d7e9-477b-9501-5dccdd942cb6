# Generated by Django 2.2 on 2022-04-17 19:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("goal_mission", "0001_initial"),
        ("mission", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="missiongoal",
            name="mission",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="mission.Mission", verbose_name="Mission"
            ),
        ),
        migrations.AddField(
            model_name="goalkeyresulttype",
            name="goal_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="goal_mission.GoalType", verbose_name="Goal Type"
            ),
        ),
        migrations.AddField(
            model_name="goalkeyresult",
            name="goal_key_result_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="goal_mission.GoalKeyResultType",
                verbose_name="Goal Key Result Type",
            ),
        ),
        migrations.AddField(
            model_name="goalkeyresult",
            name="mission_goal",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="goal_mission.MissionGoal", verbose_name="Mission Goal"
            ),
        ),
    ]
