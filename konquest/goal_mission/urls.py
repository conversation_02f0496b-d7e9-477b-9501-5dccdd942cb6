from django.urls import path
from goal_mission.viewsets.goal_key_result_type_viewset import GoalKeyResultTypeViewSet
from goal_mission.viewsets.goal_key_result_viewset import GoalKeyResultViewSet
from goal_mission.viewsets.goal_type_viewset import GoalTypeViewSet
from goal_mission.viewsets.mission_goal_viewset import MissionGoalViewSet

_READ_ONLY = {"get": "list"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("/goal-key-result-types", GoalKeyResultTypeViewSet.as_view(_LIST), name="goal-key-result-types-list"),
    path(
        "/goal-key-result-types/<uuid:pk>",
        GoalKeyResultTypeViewSet.as_view(_DETAIL),
        name="goal-key-result-types-detail",
    ),
    path("/goal-key-results", GoalKeyResultViewSet.as_view(_LIST), name="goal-key-result-list"),
    path("/goal-key-results/<uuid:pk>", GoalKeyResultViewSet.as_view(_DETAIL), name="goal-key-result-detail"),
    path("/goal-types", GoalTypeViewSet.as_view(_LIST), name="goal-types-list"),
    path("/goal-types/<uuid:pk>", GoalTypeViewSet.as_view(_DETAIL), name="goal-types-detail"),
    path("", MissionGoalViewSet.as_view(_LIST), name="mission-goals-list"),
    path("/<uuid:pk>", MissionGoalViewSet.as_view(_DETAIL), name="mission-goals-detail"),
]
