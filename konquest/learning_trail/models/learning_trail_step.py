import uuid

from django.db import models
from learning_trail.models.learning_trail import LearningTrail
from mission.models import Mission
from pulse.models import Pulse
from utils.models import BaseModel


class LearningTrailStep(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    learning_trail = models.ForeignKey(LearningTrail, verbose_name="Learning Trail", on_delete=models.CASCADE)

    mission = models.ForeignKey(
        Mission,
        verbose_name="Mission",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="learning_trail_step",
    )
    pulse = models.ForeignKey(
        Pulse, verbose_name="Pulse", null=True, blank=True, on_delete=models.CASCADE, related_name="learning_trail_step"
    )

    order = models.PositiveIntegerField(verbose_name="Order")

    class Meta:
        verbose_name_plural = "Learning Trail Steps"
        db_table = "learning_trail_step"
