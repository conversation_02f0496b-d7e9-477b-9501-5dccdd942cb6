import uuid

from django.db import models
from utils.models import BaseModel


class LearningTrailType(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    image = models.URLField(verbose_name="Learning Trail Type Image", null=False, blank=False)

    class Meta:
        verbose_name_plural = "Learning Trail Types"
        db_table = "learning_trail_type"
