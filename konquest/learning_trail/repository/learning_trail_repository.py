from account.models import User
from authentication.keeps_permissions import ADMIN, SUPER_ADMIN
from config import settings
from django.db.models import Case, IntegerField, Q, QuerySet, When
from group.services import GroupFilterService
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from user_activity.models import LearningTrailEnrollment

OTHERS = "OTHERS"


class LearningTrailListRepository:
    def __init__(self, group_filter_service: GroupFilterService):
        self._group_service = group_filter_service
        self._filters_by_role = {
            SUPER_ADMIN: self.get_trails_workspace,
            ADMIN: self.get_trails_workspace,
            OTHERS: self._get_allowed_learning_trails,
        }

    def get_trails(self, user_id: str, workspace_id: str, role: str, include_enrolled: bool = True) -> QuerySet:
        if role not in self._filters_by_role:
            role = OTHERS
        user = User.objects.get(id=user_id)
        return self._filters_by_role[role](user_id, workspace_id, include_enrolled, user.language)

    def get_trail_steps(self, user_id: str, workspace_id: str, role: str, include_enrolled: bool = True) -> QuerySet:
        trails = self.get_trails(user_id, workspace_id, role, include_enrolled)
        return LearningTrailStep.objects.filter(learning_trail_id__in=trails.values_list("id", flat=True))

    def get_trails_workspace(
        self,
        user_id: str,
        workspace_id: str,
        include_enrolled: bool,
        user_language: str = None,
        relationship_type: str = None,
    ) -> QuerySet:
        trail_workspaces = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id)
        if relationship_type:
            trail_workspaces.filter(relationship_type=relationship_type)
        trails = LearningTrail.objects.filter(id__in=trail_workspaces.values_list("learning_trail_id", flat=True))
        return self.order_query_by_user_language(trails, user_language)

    @staticmethod
    def get_allowed_learning_trails_user_creator(workspace_id: str, user_id: str) -> QuerySet:
        workspace_trails = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id).values_list(
            "learning_trail_id", flat=True
        )
        manageable_trails = LearningTrail.objects.filter(Q(id__in=workspace_trails, user_creator=user_id))
        return manageable_trails

    def _get_allowed_learning_trails(
        self, user_id: str, workspace_id: str, include_enrolled: bool, user_language=None
    ) -> QuerySet:
        base_query = self.base_query(workspace_id, user_id, include_enrolled)
        trails = self.order_query_by_user_language(base_query, user_language) if user_language else base_query

        return trails

    @staticmethod
    def order_query_by_user_language(base_query: QuerySet, language: str) -> QuerySet:
        query = base_query.annotate(
            lang=Case(When(language=language, then=1), default=0, output_field=IntegerField())
        ).order_by("-lang")
        return query

    def base_query(self, workspace_id: str, user_id: str, include_enrolled: bool) -> QuerySet:
        excluded_trail_ids = []

        workspace_lts = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id).values_list(
            "learning_trail_id", flat=True
        )

        workspace_lts_type_name_allowed = [settings.LEARNING_TRAIL_OPEN_FOR_COMPANY]

        lts_type_allowed = LearningTrailType.objects.filter(name__in=workspace_lts_type_name_allowed).values_list(
            "id", flat=True
        )
        enrolled_trail_ids = LearningTrailEnrollment.objects.filter(
            user_id=user_id, give_up=False, workspace_id=workspace_id
        ).values_list("learning_trail_id", flat=True)

        if not include_enrolled:
            excluded_trail_ids = enrolled_trail_ids
            enrolled_trail_ids = []

        learning_trail_group = self._group_service.learning_trail_filter(workspace_id, user_id)

        base_query = LearningTrail.objects.filter(
            (
                Q(id__in=workspace_lts, learning_trail_type_id__in=lts_type_allowed)
                | Q(id__in=learning_trail_group)
                | Q(id__in=workspace_lts, user_creator=user_id)
                | Q(id__in=enrolled_trail_ids)
            )
            & ~Q(id__in=excluded_trail_ids)
        )

        return base_query
