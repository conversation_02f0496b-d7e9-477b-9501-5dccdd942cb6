from abc import ABC, abstractmethod

from account.models import User
from constants import ACTION_LIST
from django.db.models import Q
from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission
from mission.models.mission_workspace import OWNER
from notification.dtos.message_v2 import MessageV2
from notification.services.notification_service_v2 import NotificationServiceV2


class MissionTrailUpdatedStatusNotificationService(ABC):
    def __init__(self, notification_service: NotificationServiceV2, type_key: str):
        self._notification_service = notification_service
        self._type_key = type_key

    def send(self, mission: Mission, user_modifier: User):
        trail_ids = LearningTrailStep.objects.filter(
            Q(mission_id=mission.id), ~Q(learning_trail__user_creator_id=mission.user_creator_id)
        ).values_list("learning_trail_id", flat=True)
        trails = LearningTrail.objects.filter(id__in=trail_ids)
        users_notified = []
        for trail in trails:
            if trail.user_creator_id in users_notified:
                continue
            self._send_by_trail(trail, mission, user_modifier)
            users_notified.append(trail.user_creator_id)

    def _send_by_trail(self, trail: LearningTrail, mission: Mission, user_modifier: User):
        trail_workspace = trail.learningtrailworkspace_set.filter(relationship_type=OWNER).first()
        if not trail_workspace:
            return
        self._send_notification(trail, mission, user_modifier, trail_workspace.workspace_id)
        self._send_email(trail, mission, trail_workspace.workspace_id)

    @staticmethod
    def _get_message(mission_name: str, trail_name: str, modifier_user_name: str) -> MessageV2:
        raise NotImplementedError("_get_message not implemented")

    @staticmethod
    @abstractmethod
    def _send_email(trail: LearningTrail, mission: Mission, workspace_id: str):
        raise NotImplementedError("_send_email not implemented")

    def _send_notification(self, trail: LearningTrail, mission: Mission, user_modifier: User, workspace_id: str):
        message = self._get_message(mission.name, trail.name, user_modifier.name)
        self._notification_service.create_notification(
            user_ids=[str(trail.user_creator_id)],
            workspace_id=workspace_id,
            type_key=self._type_key,
            action=ACTION_LIST,
            message=message,
            object=trail.id,
        )
