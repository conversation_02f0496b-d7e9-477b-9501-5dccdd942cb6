from datetime import timedelta

import mock
from account.models import User, Workspace
from conftest import NotificationServiceStub
from constants import ACTION_LIST
from django.test import TestCase
from django.utils.timezone import now
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailWorkspace
from learning_trail.services.notifications.mission_trail_disabled_notification_service import MISSION_DISABLED
from learning_trail.services.notifications.mission_trail_disabled_too_long_notification_service import (
    THE_MISSION_X_LINKED_IN_TRAIL_Y_IS_DISABLED_FOR_Z_DAYS,
    MissionTrailDisabledTooLongNotificationService,
)
from mission.models import Mission
from model_mommy import mommy
from notification.dtos.message_v2 import MessageV2


class TestMissionTrailDisabledTooLongNotificationService(TestCase):
    def setUp(self) -> None:
        self.service = MissionTrailDisabledTooLongNotificationService(NotificationServiceStub())

    @mock.patch.object(NotificationServiceStub, NotificationServiceStub.create_notification.__name__)
    def test_should_send(self, create_notification: mock.MagicMock):
        days = 3
        mission = mommy.make(Mission)
        mission.updated_date = now() - timedelta(days=days)
        trail = mommy.make(LearningTrail, user_creator=mommy.make(User))
        mommy.make(LearningTrailStep, mission=mission, learning_trail=trail)
        workspace = mommy.make(Workspace)
        mommy.make(LearningTrailWorkspace, learning_trail=trail, workspace=workspace)

        self.service.send(mission)

        message = MessageV2(
            title=MISSION_DISABLED,
            description=THE_MISSION_X_LINKED_IN_TRAIL_Y_IS_DISABLED_FOR_Z_DAYS,
            description_values={"mission_name": mission.name, "trail_name": trail.name, "days": days},
        )
        create_notification.assert_called_with(
            user_ids=[str(trail.user_creator_id)],
            type_key="MISSION_TRAIL_DISABLED_FOR_TOO_LONG",
            action=ACTION_LIST,
            message=message,
            object=trail.id,
            workspace_id=workspace.id,
        )
