import mock
from account.models import User, Workspace
from conftest import NotificationServiceStub
from constants import ACTION_LIST
from django.test import TestCase
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailWorkspace
from learning_trail.services.notifications.mission_trail_enabled_notification_service import (
    MISSION_ENABLED,
    THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_ENABLED_BY_Z,
    MissionTrailEnabledNotificationService,
)
from mission.models import Mission
from model_mommy import mommy
from notification.dtos.message_v2 import MessageV2


class TestMissionTrailEnabledNotificationService(TestCase):
    def setUp(self) -> None:
        self.service = MissionTrailEnabledNotificationService(NotificationServiceStub())

    @mock.patch.object(NotificationServiceStub, NotificationServiceStub.create_notification.__name__)
    def test_should_send(self, create_notification: mock.MagicMock):
        mission = mommy.make(Mission)
        trail = mommy.make(LearningTrail, user_creator=mommy.make(User))
        mommy.make(LearningTrailStep, mission=mission, learning_trail=trail)
        user = mommy.make(User)
        workspace = mommy.make(Workspace)
        mommy.make(LearningTrailWorkspace, learning_trail=trail, workspace=workspace)

        self.service.send(mission, user)

        message = MessageV2(
            title=MISSION_ENABLED,
            description=THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_ENABLED_BY_Z,
            description_values={"mission_name": mission.name, "trail_name": trail.name, "user_name": user.name},
        )
        create_notification.assert_called_with(
            user_ids=[str(trail.user_creator_id)],
            type_key="MISSION_TRAIL_ENABLED",
            action=ACTION_LIST,
            message=message,
            object=trail.id,
            workspace_id=workspace.id,
        )
