import mock
import pytz
from account.models import User, Workspace
from config import settings
from conftest import NotificationServiceStub
from constants import ACTION_LIST, NOTIFY_USERS_PATH
from dateutil.utils import today
from django.forms import model_to_dict
from django.test import TestCase
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailWorkspace
from learning_trail.services.notifications.mission_trail_disabled_notification_service import (
    MISSION_DISABLED,
    THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_DISABLED_BY_Z,
    MissionTrailDisabledNotificationService,
)
from mission.models import Mission
from model_mommy import mommy
from notification.dtos.message_v2 import MessageV2
from utils.email_service.templates_keys import MISSION_TRAIL_DISABLED
from utils.utils import get_today_date_formatted, get_weekday_by_index


class TestMissionTrailDisabledNotificationService(TestCase):
    def setUp(self) -> None:
        self.service = MissionTrailDisabledNotificationService(NotificationServiceStub())

    @mock.patch.object(NotificationServiceStub, NotificationServiceStub.create_notification.__name__)
    @mock.patch(NOTIFY_USERS_PATH)
    def test_should_send(self, notify_users: mock.MagicMock, create_notification: mock.MagicMock):
        mission = mommy.make(Mission)
        trail = mommy.make(LearningTrail, user_creator=mommy.make(User))
        mommy.make(LearningTrailStep, mission=mission, learning_trail=trail)
        user = mommy.make(User)
        workspace = mommy.make(Workspace)
        mommy.make(LearningTrailWorkspace, learning_trail=trail, workspace=workspace)

        self.service.send(mission, user)

        message = MessageV2(
            title=MISSION_DISABLED,
            description=THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_DISABLED_BY_Z,
            description_values={"mission_name": mission.name, "trail_name": trail.name, "user_name": user.name},
        )
        create_notification.assert_called_with(
            user_ids=[str(trail.user_creator_id)],
            type_key="MISSION_TRAIL_DISABLED",
            action=ACTION_LIST,
            message=message,
            object=trail.id,
            workspace_id=workspace.id,
        )
        notify_users.assert_called_with(
            email_data={
                "trail_name": trail.name,
                "mission_name": mission.name,
                "user_name": trail.user_creator.name,
                "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                    workspace.hash_id, mission.id
                ),
                "now_date": get_today_date_formatted(user.time_zone),
                "now_week_day": get_weekday_by_index(today(pytz.timezone(user.time_zone)).date().weekday()),
            },
            message_key=MISSION_TRAIL_DISABLED,
            workspace_id=workspace.id,
            users_receivers=[model_to_dict(trail.user_creator)],
        )
