from uuid import UUID

from authentication.keeps_permissions import SUPER_ADMIN, USER
from config import settings
from custom import keeps_exception_handler as keeps_exception
from custom.keeps_exception_handler import KeepsPermissionError
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import transaction
from django.db.models import Case, IntegerField, Q, QuerySet, When
from django.utils.timezone import now
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from group.services import GroupFilterService
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.models.mission_development_status_enum import INACTIVATED
from mission.services.mission_service import MissionService
from pulse.models import Pulse
from pulse.services.channel_service import ChannelService
from user_activity.models import LearningTrailEnrollment

NOT_PERMISSION_TO_MODIFY_THIS_LEARNING_TRAIL = gettext_noop("not_permission_to_modify_this_learning_trail")


class LearningTrailService:
    def __init__(
        self, group_filter: GroupFilterService, mission_service: MissionService, channel_service: ChannelService
    ):
        self._group_filter = group_filter
        self._mission_service = mission_service
        self._channel_service = channel_service

    @staticmethod
    @transaction.atomic()
    def delete_instance(trail: LearningTrail) -> None:
        deleted_date = now()
        trail.deleted = True
        trail.deleted_date = now()
        trail.learningtrailworkspace_set.update(deleted=True, deleted_date=deleted_date)
        trail.learningtrailenrollment_set.update(deleted=True, deleted_date=deleted_date)
        trail.grouplearningtrail_set.update(deleted=True, deleted_date=deleted_date)
        trail.learningtrailstep_set.update(deleted=True, deleted_date=deleted_date)
        trail.save()

    def get_allowed_workspace_learning_trails(self, workspace_id, user_id, include_enrolled, user_language):
        """
        List learning Trails filtering by workspace.

        :param workspace_id:
        :param user_language:
        :param include_enrolled:
        :param user_id:
        :return:
        """
        base_query = self.base_query(workspace_id, user_id, include_enrolled)
        trails = self.order_query_by_user_language(base_query, user_language) if user_language else base_query

        return trails

    @staticmethod
    def get_allowed_learning_trails_user_creator(workspace_id, user_id):
        """
        User created learning trails

        This include just learning trails where user_consumer_1_id is owner.

        :param workspace_id: workspace select in session (header x-client)
        :param user_id: should be a logged user or given by param
        :return: query
        """
        workspace_lts = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id).values_list(
            "learning_trail_id", flat=True
        )

        return LearningTrail.objects.filter(Q(id__in=workspace_lts, user_creator=user_id))

    def get_allowed_learning_trails(self, workspace_id, user_id, include_enrolled, user_language=None) -> QuerySet:
        """
        List learning trails filtering by workspace and user.
        Checking if request want include Learning Trails where user logged are enrolled.

        :param workspace_id:
        :param user_id:
        :param include_enrolled:
        :param user_language:
        :return:
        """
        base_query = self.base_query(workspace_id, user_id, include_enrolled)
        trails = self.order_query_by_user_language(base_query, user_language) if user_language else base_query

        return trails

    def base_query(self, workspace_id, user_id, include_enrolled):
        lst_enrollment = []

        workspace_lts = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id).values_list(
            "learning_trail_id", flat=True
        )

        workspace_lts_type_name_allowed = [settings.LEARNING_TRAIL_OPEN_FOR_COMPANY]

        lts_type_allowed = LearningTrailType.objects.filter(name__in=workspace_lts_type_name_allowed).values_list(
            "id", flat=True
        )

        if not include_enrolled:
            lst_enrollment = LearningTrailEnrollment.objects.filter(
                user_id=user_id, give_up=False, workspace_id=workspace_id
            ).values_list("learning_trail_id", flat=True)

        learning_trail_group = self._group_filter.learning_trail_filter(workspace_id, user_id)

        base_query = LearningTrail.objects.filter(
            (
                Q(id__in=workspace_lts, learning_trail_type_id__in=lts_type_allowed)
                | Q(id__in=learning_trail_group)
                | Q(id__in=workspace_lts, user_creator=user_id)
            )
            & ~Q(id__in=lst_enrollment)
        )

        return base_query

    @staticmethod
    def get_trails_workspace(workspace_id: str, relationship_type: str = None) -> QuerySet:
        trail_companies = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id)
        if relationship_type:
            trail_companies.filter(relationship_type=relationship_type)
        trail_ids = trail_companies.values_list("learning_trail_id", flat=True)
        return LearningTrail.objects.filter(id__in=trail_ids)

    def check_step_request_data_before_create(
        self, workspace_id: str, user_request_id: str, request_data: dict, user_role: str
    ) -> None:
        """
        Function to check the request data before create a learning trail step
        """
        try:
            learning_trail_id = request_data.get("learning_trail")
            try:
                trail = LearningTrail.objects.get(id=learning_trail_id)
            except ObjectDoesNotExist as exc:
                raise keeps_exception.KeepsBadRequestError(
                    i18n="learning_trail_not_found",
                    detail="The Learning Trail was not found or your don't have permission to this Learning Trail",
                ) from exc

            pulse_id = request_data.get("pulse")
            mission_id = request_data.get("mission")
            self.check_user_permission_to_modify_learning_trail(trail, user_request_id, workspace_id, user_role)
            if mission_id:
                self.check_step_mission(workspace_id, learning_trail_id, mission_id)
            if pulse_id:
                self.check_step_pulse(workspace_id, learning_trail_id, pulse_id)

        except ValidationError as e:
            raise keeps_exception.KeepsBadRequestError(i18n="not_valid_uuid", detail=str(e))

    def check_step_pulse(self, workspace_id: str, learning_trail_id: str, pulse_id: str) -> None:
        """
        check if the pulse in the request_data to create a learning trail step is valid
        """
        workspace_pulses_ids = self._channel_service.get_workspace_pulses(workspace_id).values_list("id", flat=True)
        if UUID(pulse_id) not in workspace_pulses_ids:
            raise keeps_exception.KeepsBadRequestError(
                i18n="pulse_not_found", detail="The pulse was not found or your don't have permission to this pulse"
            )

        already_linked = LearningTrailStep.objects.filter(pulse_id=pulse_id, learning_trail=learning_trail_id).exists()
        if already_linked:
            raise keeps_exception.KeepsBadRequestError(
                i18n="pulse_already_linked_in_learning_trail",
                detail="The pulse is already linked in the learning trail",
            )

    def check_step_mission(self, workspace_id: str, learning_trail_id: str, mission_id: str) -> None:
        """
        check if the mission in the request_data to create a learning trail step is valid
        """
        workspace_missions_ids = self._mission_service.get_workspace_missions(workspace_id).values_list("id", flat=True)
        if UUID(mission_id) not in workspace_missions_ids:
            raise keeps_exception.KeepsBadRequestError(
                i18n="mission_not_found",
                detail="The Mission was not found or your don't have permission to this Mission",
            )
        already_linked = LearningTrailStep.objects.filter(
            mission_id=mission_id, learning_trail=learning_trail_id
        ).exists()
        if already_linked:
            raise keeps_exception.KeepsBadRequestError(
                i18n="mission_already_linked_in_learning_trail",
                detail="The Mission is already linked in the learning trail",
            )

    def get_available_missions_for_learning_trail(self, learning_trail: LearningTrail, workspace_id) -> QuerySet:
        mission_ids_already_linked = LearningTrailStep.objects.filter(
            learning_trail=learning_trail, mission__isnull=False
        ).values_list("mission_id", flat=True)
        allowed_missions = self._mission_service.get_workspace_missions(workspace_id)
        allowed_missions = allowed_missions.filter(
            ~Q(development_status=INACTIVATED) & ~Q(id__in=mission_ids_already_linked)
        )

        return allowed_missions

    def get_available_pulses_for_learning_trail(self, learning_trail: LearningTrail, workspace_id: str) -> QuerySet:
        pulse_ids_already_linked = LearningTrailStep.objects.filter(
            learning_trail=learning_trail, pulse__isnull=False
        ).values_list("pulse_id", flat=True)

        allowed_pulse_ids = self._channel_service.get_workspace_pulses(workspace_id).values_list("id", flat=True)
        pulses_queryset = Pulse.objects.filter(Q(id__in=allowed_pulse_ids) & ~Q(id__in=pulse_ids_already_linked))
        return pulses_queryset

    def check_user_permission_to_modify_learning_trail(
        self, learning_trail: LearningTrail, user_id: str, workspace_id: str, user_role: str = USER
    ) -> None:
        if user_role == SUPER_ADMIN:
            is_trail_workspace = self.get_trails_workspace(workspace_id, "OWNER").filter(id=learning_trail.id).exists()
            if not is_trail_workspace:
                raise KeepsPermissionError(_(NOT_PERMISSION_TO_MODIFY_THIS_LEARNING_TRAIL))
            return

        user_creator = str(learning_trail.user_creator.id) == user_id
        if not user_creator:
            raise KeepsPermissionError(_(NOT_PERMISSION_TO_MODIFY_THIS_LEARNING_TRAIL))

    def get_missions_steps(self, learning_trail_ids: list) -> LearningTrailStep:
        return LearningTrailStep.objects.filter(learning_trail_id__in=learning_trail_ids, mission__isnull=False)

    @staticmethod
    def order_query_by_user_language(base_query: QuerySet, language: str) -> QuerySet:
        query = base_query.annotate(
            lang=Case(When(language=language, then=1), default=0, output_field=IntegerField())
        ).order_by("-lang")
        return query

    @transaction.atomic
    def reorder_steps(
        self, trail_id: str, user_modifier_id: str, steps_order: dict, workspace_id: str, user_role
    ) -> list:
        trail = LearningTrail.objects.get(id=trail_id)
        self.check_user_permission_to_modify_learning_trail(trail, user_modifier_id, workspace_id, user_role)
        steps_updated = []

        for step in steps_order:
            instance = LearningTrailStep.objects.get(id=step["step_id"])
            instance.order = step["order"]
            instance.save()
            steps_updated.append({"step_id": str(instance.id), "order": instance.order})

        return steps_updated

    @staticmethod
    def update_trail(trail_id, trail_data) -> LearningTrail:
        trail = LearningTrail.objects.get(id=trail_id)
        trail.update(**trail_data)

        trail.save()

        return trail

    @staticmethod
    def update_trail_step(step_id: str, step_data: dict) -> LearningTrailStep:
        step = LearningTrailStep.objects.get(id=step_id)
        step.__dict__.update(**step_data)

        step.save()

        return step
