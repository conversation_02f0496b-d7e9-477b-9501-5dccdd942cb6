from django.utils.translation import gettext_noop
from learning_trail.models import LearningTrail
from learning_trail.services.abstracts.mission_trail_updated_status_notification_service import (
    MissionTrailUpdatedStatusNotificationService,
)
from mission.models import Mission
from notification.dtos.message_v2 import MessageV2
from notification.services.notification_service_v2 import NotificationServiceV2

MISSION_ENABLED = gettext_noop("mission_enabled")
THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_ENABLED_BY_Z = gettext_noop("the_mission_x_linked_in_trail_y_was_enabled_by_z")
THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_ENABLED = gettext_noop("the_mission_x_linked_in_trail_y_was_enabled")


class MissionTrailEnabledNotificationService(MissionTrailUpdatedStatusNotificationService):
    def __init__(self, notification_service: NotificationServiceV2):
        super().__init__(notification_service, "MISSION_TRAIL_ENABLED")

    @staticmethod
    def _get_message(mission_name: str, trail_name: str, modifier_user_name: str) -> MessageV2:
        if modifier_user_name:
            return MessageV2(
                title=MISSION_ENABLED,
                description=THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_ENABLED_BY_Z,
                description_values={
                    "mission_name": mission_name,
                    "trail_name": trail_name,
                    "user_name": modifier_user_name,
                },
            )
        else:
            return MessageV2(
                title=MISSION_ENABLED,
                description=THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_ENABLED,
                description_values={"mission_name": mission_name, "trail_name": trail_name},
            )

    @staticmethod
    def _send_email(trail: LearningTrail, mission: Mission, workspace_id: str):
        return
