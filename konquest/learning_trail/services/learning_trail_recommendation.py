from django.db.models import <PERSON>, IntegerField, When
from injector import Provider, inject
from learning_trail.models import LearningTrailStep
from learning_trail.services.learning_trail_service import LearningTrailService
from mission.services.mission_recommendation import MissionRecommendationService


class LearningTrailRecommendationService:
    @inject
    def __init__(
        self,
        mission_recommendation_service: MissionRecommendationService = Provider[MissionRecommendationService],
        learning_trail_service: LearningTrailService = Provider[LearningTrailService],
    ):
        self.__mission_recommendation = mission_recommendation_service
        self.__lt_service = learning_trail_service

    def get_recommendations(self, user_id: str, workspace_id: str, user_language: str, recommendation_only=False):
        """
        recommend learning trails that have the recommended missions

        :param user_id: should be a logged user or given by param
        :param workspace_id: workspace select in session (header x-client)
        :param user_language: user logged language to order recommendations
        :param recommendation_only: return only the recommendations
        :return: Learning Trail Queryset
        """
        missions_lt = self.get_missions_recommendation(workspace_id, user_id, user_language)
        lts_allowed = (
            self.__lt_service.get_allowed_learning_trails(
                user_id=user_id, workspace_id=workspace_id, include_enrolled=False, user_language=user_language
            )
            .filter(is_active=True)
            .order_by("?")
        )
        lts_recommendation = lts_allowed.annotate(
            ord=Case(When(id__in=missions_lt, then=1), default=0, output_field=IntegerField())
        ).order_by("-ord")

        return lts_recommendation.filter(ord__gt=0) if (recommendation_only and missions_lt) else lts_recommendation

    def get_missions_recommendation(self, workspace_id, user_id, user_language):
        missions_recommendation = self.__mission_recommendation.get_recommendations(
            user_id=user_id, workspace_id=workspace_id, user_language=user_language
        )
        missions_lt = LearningTrailStep.objects.filter(mission_id__in=missions_recommendation).values_list(
            "learning_trail", flat=True
        )
        return missions_lt
