from learning_trail.listeners.mission_trail_disabled_notification import MissionTrailDisabledNotificationListener
from learning_trail.listeners.mission_trail_enabled_notification import MissionTrailEnabledNotificationListener
from mission.event_types import MISSION_ENABLED, MISSION_DEACTIVATED
from observer.event_manager import EventManager

event_manager = EventManager()
event_manager.subscribe(MISSION_DEACTIVATED, MissionTrailDisabledNotificationListener())
event_manager.subscribe(MISSION_ENABLED, MissionTrailEnabledNotificationListener())
