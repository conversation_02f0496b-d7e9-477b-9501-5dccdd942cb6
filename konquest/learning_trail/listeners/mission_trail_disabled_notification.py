from constants import SUB
from custom.global_requests import get_current_user
from learning_trail.tasks.notifications import notify_mission_trail_status_updated
from mission.models import Mission
from observer.event_listener import EventListener


class MissionTrailDisabledNotificationListener(EventListener):
    def update(self, mission_id: str) -> None:
        mission = Mission.objects.get(id=mission_id)
        if not mission.learning_trail_linked:
            return
        user = get_current_user()
        notify_mission_trail_status_updated.delay(mission.id, user[SUB] if user else None, False)
