# Generated by Django 2.2 on 2022-06-22 15:00
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("account", "0002_auto_20220622_1500"),
        ("learning_trail", "0002_auto_20220417_1927"),
    ]

    operations = [
        migrations.RenameModel("LearningTrailCompany", "LearningTrailWorkspace"),
        migrations.AlterField(
            model_name="learningtrail",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.RenameField(model_name="learningtrailworkspace", old_name="company", new_name="workspace"),
        migrations.AlterField(
            model_name="learningtrailworkspace",
            name="workspace",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="account.Workspace", verbose_name="Workspace"
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="learningtrailstep",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="learningtrailtype",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterUniqueTogether(
            name="learningtrailworkspace",
            unique_together={("learning_trail", "workspace")},
        ),
        migrations.AlterModelOptions(
            name="learningtrailworkspace",
            options={"verbose_name_plural": "Learning Trails Workspaces"},
        ),
        migrations.AlterField(
            model_name="learningtrailworkspace",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterModelTable(
            name="learningtrailworkspace",
            table="learning_trail_workspace",
        ),
    ]
