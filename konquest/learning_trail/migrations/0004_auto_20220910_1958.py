# Generated by Django 2.2 on 2022-09-10 19:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("learning_trail", "0003_auto_20220622_1500"),
    ]

    operations = [
        migrations.AddField(
            model_name="learningtrail",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="learningtrail",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="learningtrailstep",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="learningtrailstep",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="learningtrailtype",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="learningtrailtype",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="learningtrailworkspace",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="learningtrailworkspace",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AlterField(
            model_name="learningtrailstep",
            name="mission",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="learning_trail_step",
                to="mission.Mission",
                verbose_name="Mission",
            ),
        ),
        migrations.AlterField(
            model_name="learningtrailstep",
            name="pulse",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="learning_trail_step",
                to="pulse.Pulse",
                verbose_name="Pulse",
            ),
        ),
    ]
