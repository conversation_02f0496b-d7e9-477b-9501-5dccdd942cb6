# Generated by Django 2.2 on 2022-04-17 19:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("learning_trail", "0001_initial"),
        ("pulse", "0001_initial"),
        ("mission", "0001_initial"),
        ("account", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="learningtrailstep",
            name="mission",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="mission.Mission",
                verbose_name="Mission",
            ),
        ),
        migrations.AddField(
            model_name="learningtrailstep",
            name="pulse",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="pulse.Pulse",
                verbose_name="Pulse",
            ),
        ),
        migrations.AddField(
            model_name="learningtrailcompany",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="account.Company", verbose_name="Company"
            ),
        ),
        migrations.AddField(
            model_name="learningtrailcompany",
            name="learning_trail",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="learning_trail.LearningTrail",
                verbose_name="Learning Trail",
            ),
        ),
        migrations.AddField(
            model_name="learningtrail",
            name="learning_trail_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="learning_trail.LearningTrailType",
                verbose_name="Learning Trail Type",
            ),
        ),
        migrations.AddField(
            model_name="learningtrail",
            name="user_creator",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="account.User", verbose_name="User Creator"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="learningtrailcompany",
            unique_together={("learning_trail", "company")},
        ),
    ]
