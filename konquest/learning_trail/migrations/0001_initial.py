# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="LearningTrail",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
                ("holder_image", models.URLField(blank=True, null=True, verbose_name="Holder Image")),
                ("thumb_image", models.URLField(blank=True, null=True, verbose_name="Thumb Image")),
                ("duration_time", models.FloatField(default=0, null=True, verbose_name="Duration Time")),
                ("points", models.PositiveIntegerField(default=0, null=True, verbose_name="Points")),
                ("is_active", models.BooleanField(default=True, verbose_name="Is Active?")),
                (
                    "language",
                    models.CharField(
                        choices=[("pt-BR", "Português(BR)"), ("en", "English"), ("es", "Español")],
                        default="pt-BR",
                        max_length=20,
                        verbose_name="Learning Trail",
                    ),
                ),
                ("expiration_date", models.DateField(null=True, verbose_name="Expiration Date")),
            ],
            options={
                "verbose_name_plural": "Learning Trails",
                "db_table": "learning_trail",
            },
        ),
        migrations.CreateModel(
            name="LearningTrailCompany",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "relationship_type",
                    models.CharField(
                        choices=[("OWNER", "OWNER"), ("SHARED", "SHARED")],
                        default="OWNER",
                        max_length=8,
                        verbose_name="Development Status",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Learning Trails Companies",
                "db_table": "learning_trail_company",
            },
        ),
        migrations.CreateModel(
            name="LearningTrailType",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
                ("image", models.URLField(verbose_name="Learning Trail Type Image")),
            ],
            options={
                "verbose_name_plural": "Learning Trail Types",
                "db_table": "learning_trail_type",
            },
        ),
        migrations.CreateModel(
            name="LearningTrailStep",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("order", models.PositiveIntegerField(verbose_name="Order")),
                (
                    "learning_trail",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="learning_trail.LearningTrail",
                        verbose_name="Learning Trail",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Learning Trail Steps",
                "db_table": "learning_trail_step",
            },
        ),
    ]
