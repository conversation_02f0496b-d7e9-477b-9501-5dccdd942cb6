from learning_trail.models import LearningTrailStep
from learning_trail.serializers.learning_trail_step_mission_short_serialzier import (
    LearningTrailStepMissionShortSerializer,
)
from learning_trail.serializers.learning_trail_step_pulse_short_serializer import LearningTrailStepPulseShortSerializer
from rest_framework import serializers


class LearningTrailStepShortSerializer(serializers.ModelSerializer):
    mission = LearningTrailStepMissionShortSerializer()
    pulse = LearningTrailStepPulseShortSerializer()

    class Meta:
        model = LearningTrailStep
        fields = "__all__"
