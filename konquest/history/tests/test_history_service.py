import uuid

from auditlog.models import LogEntry
from django.contrib.contenttypes.models import ContentType
from django.test import TestCase
from history.history_service import HistoryService
from model_mommy import mommy


class TestHistoryService(TestCase):
    def setUp(self):
        self.service = HistoryService("mission", "mission")
        self.mission_id = str(uuid.uuid4())

    def test_should_list_mission_history(self):
        self.create_mission_history()

        queryset = self.service.list_history(self.mission_id)

        self.assertEqual(queryset.count(), 2)
        self.assertEqual(queryset.first().action, 1)

    def create_mission_history(self):
        content_type = ContentType.objects.get(app_label="mission", model="mission")
        mommy.make(LogEntry, object_pk=self.mission_id, action=0, content_type=content_type)
        mommy.make(LogEntry, object_pk=self.mission_id, action=1, content_type=content_type)
        mommy.make(LogEntry, object_pk=uuid.uuid4(), action=2, content_type=content_type)
