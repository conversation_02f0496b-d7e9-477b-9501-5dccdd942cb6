from auditlog.models import LogEntry
from django.test import TestCase
from history.history_serializer import HistorySerializer


class TestHistorySerializer(TestCase):
    def setUp(self):
        self.serializer = HistorySerializer

    def test_should_serializer(self):
        log_entry = LogEntry(
            action=0,
            changes='{"minimum_performance": ["0.2", "0.1"]}',
            additional_data={
                "user_actor": {
                    "id": "bbf47825-8dfb-49bc-8ad8-f8adc775f95f",
                    "name": "Super Admin Admin",
                    "email": "<EMAIL>",
                }
            },
        )
        serializer = self.serializer(instance=log_entry)
        self.assertEqual(
            serializer.data,
            {
                "action": "CREATED",
                "actor": {
                    "email": "<EMAIL>",
                    "id": "bbf47825-8dfb-49bc-8ad8-f8adc775f95f",
                    "name": "Super Admin Admin",
                },
                "changes": {"minimum_performance": ["0.2", "0.1"]},
                "id": None,
                "object_pk": "",
                "object_repr": "",
            },
        )
