from auditlog.models import LogEntry
from django.db.models import QuerySet


class HistoryService:
    def __init__(self, app_label: str, model_name: str):
        self.app_label = app_label
        self.model_name = model_name

    def list_history(self, object_pk: str) -> QuerySet:
        return LogEntry.objects.filter(
            content_type__app_label=self.app_label, content_type__model=self.model_name, object_pk=object_pk
        ).order_by("-timestamp")
