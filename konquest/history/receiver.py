from auditlog.models import LogEntry
from custom.global_requests import get_current_user
from django.db.models.signals import pre_save
from django.dispatch import receiver


@receiver(pre_save, sender=LogEntry)
def log_entry_modifier(sender, instance: LogEntry, **kwargs):
    user = get_current_user()
    if not isinstance(user, dict):
        return
    instance.additional_data = {
        "user_actor": {"id": str(user.get("sub")), "name": user.get("name"), "email": user.get("email")}
    }
