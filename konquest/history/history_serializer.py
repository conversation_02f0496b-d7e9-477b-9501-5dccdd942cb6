import json

from auditlog.models import LogEntry
from rest_framework import serializers


class HistorySerializer(serializers.ModelSerializer):
    changes = serializers.SerializerMethodField()
    action = serializers.SerializerMethodField()
    actor = serializers.SerializerMethodField()

    @staticmethod
    def get_actor(log_entry: LogEntry):
        return log_entry.additional_data.get("user_actor", "UNKNOWN")

    @staticmethod
    def get_action(log_entry: LogEntry):
        actions = {0: "CREATED", 1: "UPDATED", 3: "DELETED"}
        return actions.get(log_entry.action, "OTHER")

    @staticmethod
    def get_changes(log_entry: LogEntry):
        return json.loads(log_entry.changes)

    class Meta:
        model = LogEntry
        fields = ("id", "changes", "object_pk", "object_repr", "action", "actor")
