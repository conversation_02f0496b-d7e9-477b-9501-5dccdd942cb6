from category.exceptions.category_name_already_exists import CategoryNameAlreadyExists
from category.models import CustomCategory
from custom.keeps_exception_handler import KPNotAllowedDeleteCategoryLinkedToAChannelOrMission
from django.db import transaction
from django.utils.timezone import now
from mission.models import Mission, MissionCategory
from pulse.models import Channel, ChannelCategory


class CategoryService:
    @staticmethod
    def _check_category_is_used(category: CustomCategory):
        channel_categories = ChannelCategory.objects.filter(name=category.name, workspace=category.workspace)
        mission_categories = MissionCategory.objects.filter(name=category.name, workspace=category.workspace)
        category_channels = Channel.objects.filter(
            channel_category_id__in=channel_categories.values_list("id", flat=True)
        )
        category_missions = Mission.objects.filter(
            mission_category_id__in=mission_categories.values_list("id", flat=True)
        )
        if category_channels.exists() or category_missions.exists():
            raise KPNotAllowedDeleteCategoryLinkedToAChannelOrMission()

    @staticmethod
    def _delete_channel_category(category: CustomCategory):
        channel_categories = ChannelCategory.objects.filter(name=category.name, workspace=category.workspace)
        channel_categories.update(deleted=True, deleted_date=now())

    @staticmethod
    def _delete_mission_category(category: CustomCategory):
        mission_categories = MissionCategory.objects.filter(name=category.name, workspace=category.workspace)
        mission_categories.update(deleted=True, deleted_date=now())

    @transaction.atomic()
    def delete_category(self, category: CustomCategory):
        self._check_category_is_used(category)
        category.deleted = True
        category.deleted_date = now()
        category.save()
        self._delete_channel_category(category)
        self._delete_mission_category(category)

    @transaction.atomic()
    def create_category(self, category: CustomCategory):
        self.exception_if_category_name_lower_exists(category)
        category_exists = CustomCategory.objects.get_all_including_deleted().filter(
            name=category.name, workspace=category.workspace
        )
        if category_exists.exists():
            category = category_exists.first()
            category.deleted = False
            category.deleted_date = None
            channel_categories = ChannelCategory.objects.get_all_including_deleted().filter(
                name=category.name, workspace=category.workspace
            )
            mission_categories = MissionCategory.objects.get_all_including_deleted().filter(
                name=category.name, workspace=category.workspace
            )
            channel_categories.update(deleted=False, deleted_date=None)
            mission_categories.update(deleted=False, deleted_date=None)
        category.save()
        return category

    @transaction.atomic()
    def update_category(self, category: CustomCategory, validated_data):
        category.update(**validated_data)
        self.exception_if_category_name_lower_exists(category)
        category.save()
        return category

    @staticmethod
    def check_category_name_lower_exists(category: CustomCategory):
        return (
            CustomCategory.objects.filter(name__iexact=category.name, workspace=category.workspace)
            .exclude(id=category.id)
            .exists()
        )

    def exception_if_category_name_lower_exists(self, category: CustomCategory):
        if self.check_category_name_lower_exists(category):
            raise CategoryNameAlreadyExists()
