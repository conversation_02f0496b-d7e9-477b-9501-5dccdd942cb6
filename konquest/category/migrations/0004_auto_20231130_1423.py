# Generated by Django 3.2.17 on 2023-11-30 14:23

from django.db import migrations
from django.db.models.functions import Lower

def normalize_custom_categories(apps, schema_editor):
    CustomCategory = apps.get_model('category', 'CustomCategory')
    Mission = apps.get_model('mission', 'Mission')
    Channel = apps.get_model('pulse', 'Channel')
    ChannelCategory = apps.get_model('pulse', 'ChannelCategory')
    MissionCategory = apps.get_model('mission', 'MissionCategory')

    uniques_category = CustomCategory.objects.all().annotate(lower_name=Lower('name')).distinct('lower_name')

    for category in uniques_category:
        duplicates = CustomCategory.objects.filter(name__iexact=category.lower_name).exclude(id=category.id)
        duplicates_ids = duplicates.values_list('id', flat=True)

        missions_in_category = Mission.objects.filter(mission_category__in=duplicates_ids)
        missions_in_category.update(
            mission_category=str(category.id)
        )
        channels_in_category = Channel.objects.filter(channel_category__in=duplicates_ids)
        channels_in_category.update(
            channel_category=str(category.id)
        )
        duplicates.delete()
        MissionCategory.objects.filter(id__in=duplicates_ids).delete()
        ChannelCategory.objects.filter(id__in=duplicates_ids).delete()
        MissionCategory.objects.filter(id=category.id).update(name=category.lower_name)
        ChannelCategory.objects.filter(id=category.id).update(name=category.lower_name)
        category.name = category.lower_name
        category.save()
        

class Migration(migrations.Migration):

    dependencies = [
        ('category', '0003_merge_20220909_1844'),
    ]

    operations = [
        migrations.RunPython(normalize_custom_categories),
    ]
