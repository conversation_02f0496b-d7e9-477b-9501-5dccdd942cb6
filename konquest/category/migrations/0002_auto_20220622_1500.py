# Generated by Django 2.2 on 2022-06-22 15:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("account", "0002_auto_20220622_1500"),
        ("category", "0001_initial"),
    ]

    operations = [
        migrations.RenameField(model_name="customcategory", new_name="workspace", old_name="company"),
        migrations.AlterField(
            model_name="customcategory",
            name="workspace",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="account.Workspace",
                verbose_name="Workspace",
            ),
        ),
        migrations.AlterField(
            model_name="customcategory",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterField(
            model_name="customcategory",
            name="description",
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name="Description"),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="customcategory",
            name="name",
            field=models.<PERSON><PERSON><PERSON><PERSON>(max_length=200, verbose_name="Name"),
        ),
        migrations.AlterUniqueTogether(
            name="customcategory",
            unique_together={("name", "workspace")},
        ),
    ]
