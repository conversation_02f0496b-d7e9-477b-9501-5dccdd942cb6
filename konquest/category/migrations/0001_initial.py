# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("account", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomCategory",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Company Name")),
                ("description", models.CharField(blank=True, max_length=200, null=True, verbose_name="Company Name")),
                ("image", models.CharField(max_length=200, verbose_name="Custom Category Image")),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="account.Company", verbose_name="Company"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Custom Categories",
                "db_table": "custom_category",
                "unique_together": {("name", "company")},
            },
        ),
    ]
