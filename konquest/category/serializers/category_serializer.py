# -*- coding: utf-8 -*-

from category.models.category import CustomCategory
from mission.models import Mission
from pulse.models import Channel
from rest_framework import serializers


class CustomCategoryMissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Mission
        fields = ["id", "name"]


class CustomCategoryChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = Channel
        fields = ["id", "name"]


class CustomCategorySerializer(serializers.ModelSerializer):
    missions = CustomCategoryMissionSerializer(many=True, read_only=True)
    channels = CustomCategoryChannelSerializer(many=True, read_only=True)

    class Meta:
        model = CustomCategory
        fields = "__all__"


class CustomCategoryPostSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomCategory
        fields = ["name", "description", "image"]
