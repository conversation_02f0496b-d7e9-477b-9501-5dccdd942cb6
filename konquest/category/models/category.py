import uuid

from account.models import Workspace
from django.db import models
from mission.models import Mission
from pulse.models import Channel
from utils.models import BaseModel


class CustomCategory(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON><PERSON>(verbose_name="Name", max_length=200)
    description = models.CharField(verbose_name="Description", max_length=200, null=True, blank=True)
    image = models.CharField(verbose_name="Custom Category Image", max_length=200, null=False, blank=False)

    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.PROTECT, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Custom Categories"
        db_table = "custom_category"
        unique_together = (
            "name",
            "workspace",
        )

    @property
    def missions(self):
        return Mission.objects.filter(mission_category=str(self.id), missionworkspace__workspace=self.workspace)

    @property
    def channels(self):
        return Channel.objects.filter(channel_category=str(self.id), workspace=self.workspace)
