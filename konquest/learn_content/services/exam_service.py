from typing import Sequence

from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q, QuerySet
from django.utils.translation import gettext as _
from group.services import GroupFilterService
from learn_content.models import Exam, Question, QuestionOption
from mission.models import MissionWorkspace
from pulse.models import Pulse
from pulse.selectors.channel_selector import ChannelSelector
from pulse.services.channel_service import ChannelService

# pylint: disable=too-few-public-methods


class ExamService:
    def __init__(self, channel_service: ChannelService):
        self._channel_service = channel_service
        self._selector = ChannelSelector(GroupFilterService())

    def get_exam(self, workspace_id: str, user_id: str, role: str) -> QuerySet:
        channels_allowed = self._channel_service.get_allowed_channels(workspace_id=workspace_id, user_id=user_id)
        channels_allowed = channels_allowed.values_list("id", flat=True)

        pulses_allowed = self._selector.get_allowed_pulses(user_id, workspace_id, role).values_list("id", flat=True)
        missions = MissionWorkspace.objects.filter(workspace_id=workspace_id).values_list("mission_id", flat=True)
        query = (
            Q(stage__mission_id__in=missions)
            | Q(channel_id__in=channels_allowed)
            | Q(pulse_id__in=pulses_allowed)
            | (Q(channel_id__isnull=True) & Q(pulse_id__isnull=True) & Q(stage_id__isnull=True))
        )
        return Exam.objects.filter(query).all()

    def get_questions(self, workspace_id: str, user_id: str, role: str) -> QuerySet:
        allowed_exams = self.get_exam(workspace_id, user_id, role)
        return Question.objects.filter(exam_id__in=allowed_exams.values_list("id", flat=True))

    @transaction.atomic()
    def save_pulse_exam(self, channel_id: str, pulse_data: dict, exam_data: dict, user_creator_id: str) -> Pulse:
        questions_data = exam_data.pop("questions")
        exam = self.save_exam(exam_data)
        self.save_questions(exam.id, questions_data, user_creator_id)

        pulse_data["learn_content_uuid"] = exam.id
        pulse = self._channel_service.save_pulse(channel_id, pulse_data)

        exam.pulse = pulse
        exam.channel_id = channel_id
        exam.save()

        return pulse

    @transaction.atomic()
    def save_exam(self, data: dict) -> Exam:
        exam = Exam(**data)
        exam.save()
        return exam

    @staticmethod
    def delete(exam_id):
        exam = Exam.objects.get(id=exam_id)
        exam.delete()

    @transaction.atomic()
    def save_question(self, data: dict, options_data: Sequence[dict]) -> Question:
        question = Question(**data)
        question.save()
        self._set_options(question, options_data)
        return question

    @transaction.atomic()
    def save_questions(self, exam_id: str, datas: Sequence[dict], user_creator_id: str) -> Sequence[Question]:
        questions = []
        for data in datas:
            options_data = data.pop("options")
            question = Question(**data)
            question.user_creator_id = user_creator_id
            question.exam_id = exam_id
            question.save()
            self._set_options(question, options_data)
            questions.append(question)
        return questions

    @staticmethod
    def _set_options(question: Question, options_data: Sequence[dict]) -> None:
        options = []
        for option_data in options_data:
            option = QuestionOption(**option_data)
            option.question = question
            options.append(option)

        QuestionOption.objects.bulk_create(options)

    @transaction.atomic()
    def update_question(self, question_id: str, data: dict, options_data: Sequence[dict]) -> Question:
        question = Question.objects.get(id=question_id)
        question.__dict__.update(**data)
        if options_data:
            self._update_question_options(question_id, options_data)

        if self._options_not_integrity(question):
            raise ValidationError(_("question_must_have_one_or_more_correct_options"))

        question.save()
        return question

    @staticmethod
    def _options_not_integrity(question: Question) -> bool:
        options = question.options
        no_options = not options
        no_correct_choices_type = question.question_type != "correct_choices"
        if no_correct_choices_type or no_options:
            return False

        no_correct_option = not options.filter(correct_answer=True).exists()
        if no_correct_option:
            return True
        return False

    @staticmethod
    def _update_question_options(question_id: str, options_data: Sequence[dict]) -> None:
        for op_selected in options_data:
            QuestionOption.objects.filter(id=op_selected["id"], question_id=question_id).update(**op_selected)
