from custom import KeepsBadRequestError
from learn_content.models import Answer, Question


class AnswerService:
    @staticmethod
    def save_answer(data, user_id, question_id):
        if "options" not in data or not data.get("options", ""):
            raise KeepsBadRequestError(i18n="set_options_value", detail="Need inform answer option value")

        try:
            question = Question.objects.get(id=question_id)
            user_options = data["options"]
            question_options = question.questionoption_set.all()
            correct_options = [str(entry.id) for entry in question_options if entry.correct_answer is True]
            instance = Answer()
            instance.user_id = user_id
            instance.exam_has_question_id = question_id
            instance.is_ok = set(correct_options) == set(user_options)
            instance.options = ",".join(user_options)

            if data.get("enrollment", None):
                instance.enrollment_id = data.get("enrollment", None)

            instance.save()

            return instance
        except Exception as exc:
            raise KeepsBadRequestError(i18n="error_to_save_exam_answer", detail=str(exc)) from exc

    @staticmethod
    def save_batch_answer(data, user_id):
        answers_data = data["questions"]
        answers = []
        for answer in answers_data:
            if answer["options"] == "" or answer is None:
                raise KeepsBadRequestError(i18n="set_options_value", detail="Need inform answer option value")

        for answer in answers_data:
            question = Question.objects.get(id=answer["id"])
            question_options = question.questionoption_set.all()
            correct_options = [str(entry.id) for entry in question_options if entry.correct_answer is True]
            user_options = answer["options"]
            is_ok = set(correct_options) == set(user_options)
            answer = Answer()
            answer.user_id = user_id
            answer.options = ",".join(user_options)
            answer.exam_has_question = question
            answer.is_ok = is_ok
            answer.save()
            answers.append(answer)

        return answers
