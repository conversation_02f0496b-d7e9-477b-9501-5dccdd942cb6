from uuid import uuid4

from authentication import keeps_permissions
from custom.keeps_exception_handler import KeepsMissionCoverUnsupportedMediaTypeError
from injector import Provider, inject
from learn_content.serializers.cover_image_by_sizer_serializer import CoverImageBySizerInputSerializer
from rest_framework import status, viewsets
from rest_framework.response import Response
from utils.aws import S3Client
from utils.image.editor import Editor


class CoverImageBySizeViewSet(viewsets.ViewSet):
    permission_classes = (keeps_permissions.IsAuthenticatedWithoutXClient,)
    serializer = CoverImageBySizerInputSerializer

    @inject
    def __init__(self, s3_client: S3Client = Provider[S3Client], editor: Editor = Provider[Editor], **kwargs):
        super().__init__(**kwargs)
        self._allowed_images = {"image/jpeg": "jpg", "image/png": "png", "image/webp": "jpg"}
        self._s3_client = s3_client
        self._editor = editor

    def create(self, request, *args, **kwargs):
        serializer = self.serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        image = serializer.validated_data["file"]
        width = serializer.validated_data["width"]
        height = serializer.validated_data["height"]
        source_data = image.read()
        extension = image.content_type

        if extension not in self._allowed_images:
            raise KeepsMissionCoverUnsupportedMediaTypeError()
        extension = self._allowed_images[image.content_type]

        image_path = self._editor.cut(source_data, width, height)
        destiny_name = f"cover-image/{uuid4()}-{width}x{height}.{extension}"
        url = self._s3_client.send_file_path(image_path, destiny_name, image.content_type)["url"]
        return Response({"url": url}, status=status.HTTP_200_OK)
