from uuid import uuid4

from authentication import keeps_permissions
from custom import keeps_exception_handler
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from utils.aws import S3Client
from utils.image.editor import Editor


class CoverImageViewSet(viewsets.ViewSet):
    permission_classes = (keeps_permissions.IsAuthenticatedWithoutXClient,)

    @inject
    def __init__(self, editor: Editor = Provider[Editor], s3_client: S3Client = Provider[S3Client], **kwargs):
        super().__init__(**kwargs)
        self._editor = editor
        self._allowed_images = {"image/jpeg": "jpg", "image/png": "png", "image/webp": "webp"}
        self._s3_client = s3_client

    def create(self, request, *args, **kwargs):
        file = request.data.get("file")
        if not file:
            raise keeps_exception_handler.KeepsBadRequestError("Pass 'file' in request data", "file_cannot_be_null")
        extension = file.content_type
        if extension not in self._allowed_images:
            raise keeps_exception_handler.KeepsError(
                "Image not allowed. Use PNG, WEBP or JPG", "mission_cover_unsupported_media_type", 415
            )
        extension = self._allowed_images[file.content_type]

        image_sizes = {"small": (322, 186), "vertical": (333, 592), "large": (1000, 500)}

        source_data = file.read()

        response = {}
        for size in image_sizes:
            size_values = image_sizes[size]
            width = size_values[0]
            height = size_values[1]
            image = self._editor.cut(source_data, width, height)
            url = self._s3_client.send_file_path(
                image, f"cover-image/{uuid4()}-{width}x{height}.{extension}", file.content_type
            )["url"]
            response.update({size: url})

        return Response(response, status=status.HTTP_200_OK)
