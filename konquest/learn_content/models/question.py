import uuid

from account.models.user import User
from django.db import models
from learn_content.models.exam import Exam
from utils.models import BaseModel

QUESTION_TYPE_CHOICES = (
    ("correct_choices", "correct_choices"),
    ("correct_essay", "correct_essay"),
    ("correct_fill_the_blank_order", "correct_fill_the_blank_order"),
)


class Question(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    exam = models.ForeignKey(Exam, verbose_name="Exam", on_delete=models.CASCADE)

    exam_question = models.TextField(verbose_name="Exam Question", null=False, blank=False)
    question_type = models.CharField(verbose_name="Question Type", max_length=50, choices=QUESTION_TYPE_CHOICES)
    points = models.IntegerField(verbose_name="Points")

    user_creator = models.ForeignKey(User, verbose_name="User Creator", on_delete=models.PROTECT)

    class Meta:
        app_label = "learn_content"
        verbose_name_plural = "Questions"
        db_table = "question"

    @property
    def options(self):
        return self.questionoption_set.order_by("?").all()

    @property
    def count_correct_options(self):
        return self.questionoption_set.filter(correct_answer=True).count()
