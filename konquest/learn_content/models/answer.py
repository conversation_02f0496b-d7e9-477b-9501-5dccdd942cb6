import uuid

from account.models import User
from django.db import models
from learn_content.models.question import Question
from user_activity.models import MissionEnrollment
from utils.models import BaseModel


class Answer(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    exam_has_question = models.ForeignKey(Question, verbose_name="Question", null=True, on_delete=models.CASCADE)
    options = models.TextField(verbose_name="Options", null=False, blank=False)
    user = models.ForeignKey(User, verbose_name="User", on_delete=models.CASCADE)
    enrollment = models.ForeignKey(
        MissionEnrollment, verbose_name="Enrollment", null=True, blank=True, on_delete=models.CASCADE
    )

    is_ok = models.BooleanField(verbose_name="Is ok?", default=False)

    @property
    def correct_options(self):
        all_options = self.exam_has_question.questionoption_set.all()
        return [str(entry.id) for entry in all_options if entry.correct_answer is True]

    @property
    def splited_options(self):
        return self.options.split(",")

    class Meta:
        verbose_name_plural = "Answers"
        db_table = "answer"
        unique_together = ["exam_has_question", "user", "enrollment"]
