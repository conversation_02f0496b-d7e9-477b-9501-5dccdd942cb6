import uuid

from django.db import models


class ConsumptionTransferLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    origin_user_id = models.UUIDField(verbose_name="Origin User ID", null=False)
    destination_user_id = models.UUIDField(verbose_name="Destination User ID", null=False)
    workspace_id = models.UUIDField(verbose_name="Workspace ID")
    transferred_at = models.DateTimeField(verbose_name="Transferred At", auto_now_add=True)

    class Meta:
        verbose_name_plural = "Consumption Transfer Logs"
        db_table = "consumption_transfer_log"

    def __str__(self):
        return f"Transfer from {self.origin_user_id} to {self.destination_user_id} at {self.transferred_at}"
