import os
from unittest import TestCase

import mock
from django.urls import reverse
from rest_framework.test import APIClient

SEND_FILE_PATH = "utils.aws.aws_s3.S3Client.send_file_path"


class CoverImageInputTest(TestCase):
    fixtures = ["user"]

    def setUp(self) -> None:
        self.client = APIClient()
        user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.client.force_authenticate(user={"sub": user_id})
        self.url = reverse("cover-images-by-size")

    @mock.patch(SEND_FILE_PATH)
    def test_image_upload_file_type_correct(self, send_file):
        send_file.return_value = {"url": "https://url.com"}
        with open(os.path.join(os.path.dirname(__file__), "logo.png"), "rb") as file:
            data = {"file": file, "width": 100, "height": 100}

            response = self.client.post(self.url, data=data, format="multipart")

        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(response.data.get("url"))

    @mock.patch(SEND_FILE_PATH)
    def test_image_upload_bigger_image(self, send_file):
        send_file.return_value = {"url": "https://url.com"}
        with open(os.path.join(os.path.dirname(__file__), "bigger_image.jpg"), "rb") as file:
            data = {"file": file, "width": 1000, "height": 500}

            response = self.client.post(self.url, data=data, format="multipart")

        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(response.data.get("url"))

    def test_image_upload_file_type_incorrect(self):
        with open(os.path.join(os.path.dirname(__file__), "animated.gif"), "rb") as file:
            data = {"file": file, "width": 100, "height": 100}
            response = self.client.post(self.url, data=data, format="multipart")

        self.assertEqual(response.status_code, 415)
