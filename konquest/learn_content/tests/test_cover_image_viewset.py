import tempfile
from unittest import TestCase

import mock
from django.urls import reverse
from PIL import Image
from rest_framework.test import APIClient

SEND_FILE_OBJ = "utils.aws.aws_s3.S3Client.send_file_obj"


class CoverImageInputTest(TestCase):
    fixtures = ["user"]

    def setUp(self) -> None:
        self.client = APIClient()
        user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.client.force_authenticate(user={"sub": user_id})
        self.url = reverse("cover-images")

    @mock.patch(SEND_FILE_OBJ)
    def test_image_upload_file_type_correct(self, send_file):
        image = self.create_image(".jpg")
        send_file.return_value = {"url": "https://url.com"}
        data = {"file": image}

        response = self.client.post(self.url, data=data, format="multipart")

        self.assertEqual(response.status_code, 200)
        self.assertIn("small", response.data)
        self.assertIn("vertical", response.data)
        self.assertIn("large", response.data)

    def test_image_upload_file_type_incorrect(self):
        image = self.create_image(".gif")

        data = {"file": image}

        response = self.client.post(self.url, data=data, format="multipart")

        self.assertEqual(response.status_code, 415)

    @staticmethod
    def create_image(file):
        image = Image.new("RGB", (100, 100))
        tmp_file = tempfile.NamedTemporaryFile(suffix=f"{file}")
        image.save(tmp_file)
        tmp_file.seek(0)

        return tmp_file
