# Generated by Django 5.0.4 on 2025-02-12 18:03

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0005_alter_answer_deleted_date_alter_exam_deleted_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConsumptionTransferLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('origin_user_id', models.UUIDField(verbose_name='Origin User ID')),
                ('destination_user_id', models.UUIDField(verbose_name='Destination User ID')),
                ('workspace_id', models.UUIDField(verbose_name='Workspace ID')),
                ('transferred_at', models.DateTimeField(auto_now_add=True, verbose_name='Transferred At')),
            ],
            options={
                'verbose_name_plural': 'Consumption Transfer Logs',
                'db_table': 'consumption_transfer_log',
            },
        ),
    ]
