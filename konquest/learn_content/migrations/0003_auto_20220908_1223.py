# Generated by Django 2.2 on 2022-09-08 12:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("learn_content", "0002_auto_20220417_1927"),
    ]

    operations = [
        migrations.AddField(
            model_name="answer",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="answer",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="exam",
            name="deleted",
            field=models.Bo<PERSON>an<PERSON>ield(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="exam",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="question",
            name="deleted",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="question",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="questionoption",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="questionoption",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
    ]
