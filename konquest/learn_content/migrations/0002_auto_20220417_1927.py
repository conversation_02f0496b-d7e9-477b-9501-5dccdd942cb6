# Generated by Django 2.2 on 2022-04-17 19:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("account", "0001_initial"),
        ("mission", "0001_initial"),
        ("user_activity", "0001_initial"),
        ("learn_content", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="exam",
            name="stage",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="mission.MissionStage",
                verbose_name="Stage",
            ),
        ),
        migrations.AddField(
            model_name="answer",
            name="enrollment",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="user_activity.MissionEnrollment",
                verbose_name="Enrollment",
            ),
        ),
        migrations.AddField(
            model_name="answer",
            name="exam_has_question",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="learn_content.Question",
                verbose_name="Question",
            ),
        ),
        migrations.AddField(
            model_name="answer",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="account.User", verbose_name="User"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="answer",
            unique_together={("exam_has_question", "user", "enrollment")},
        ),
    ]
