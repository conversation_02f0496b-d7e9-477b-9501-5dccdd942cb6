# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("account", "0001_initial"),
        ("pulse", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Answer",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("options", models.TextField(verbose_name="Options")),
                ("is_ok", models.BooleanField(default=False, verbose_name="Is ok?")),
            ],
            options={
                "verbose_name_plural": "Answers",
                "db_table": "answer",
            },
        ),
        migrations.CreateModel(
            name="Exam",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=100, verbose_name="Title")),
                (
                    "channel",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="pulse.Channel",
                        verbose_name="Channel",
                    ),
                ),
                (
                    "pulse",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="pulse.Pulse",
                        verbose_name="Pulse",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Exam",
                "db_table": "exam",
            },
        ),
        migrations.CreateModel(
            name="Question",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("exam_question", models.TextField(verbose_name="Exam Question")),
                (
                    "question_type",
                    models.CharField(
                        choices=[
                            ("correct_choices", "correct_choices"),
                            ("correct_essay", "correct_essay"),
                            ("correct_fill_the_blank_order", "correct_fill_the_blank_order"),
                        ],
                        max_length=50,
                        verbose_name="Question Type",
                    ),
                ),
                ("points", models.IntegerField(verbose_name="Points")),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="learn_content.Exam", verbose_name="Exam"
                    ),
                ),
                (
                    "user_creator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="account.User", verbose_name="User Creator"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Questions",
                "db_table": "question",
            },
        ),
        migrations.CreateModel(
            name="QuestionOption",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("option", models.TextField(verbose_name="Option")),
                ("correct_answer", models.BooleanField(default=False)),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="learn_content.Question", verbose_name="Type"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Question Options",
                "db_table": "question_option",
            },
        ),
    ]
