from django.urls import path
from learn_content.views.cover_image_by_size_viewset import CoverImageBySizeViewSet
from learn_content.views.cover_image_viewset import CoverImageViewSet

_SAVE_ONLY = {"post": "create"}

urlpatterns = [
    path("/cover-images", CoverImageViewSet.as_view(_SAVE_ONLY), name="cover-images"),
    path("/cover-images-by-size", CoverImageBySizeViewSet.as_view(_SAVE_ONLY), name="cover-images-by-size"),
]
