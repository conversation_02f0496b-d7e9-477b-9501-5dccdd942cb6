from account.models.user import User
from account.serializers.user_serializer import UserParserSerializer
from authentication.keeps_permissions import MANAGED_PERMISSIONS
from custom import KeepsBadRequestError
from django.utils.translation import gettext as _
from rest_framework import viewsets
from rest_framework.response import Response
from utils.utils import parser_excel


class UserParserViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = MANAGED_PERMISSIONS

    def get_queryset(self):
        return User.objects.all()

    def get_serializer_class(self):
        return UserParserSerializer

    def create(self, request, *args, **kwargs):
        file = self.request.data.get("file")
        if not file:
            raise KeepsBadRequestError(_("file_cannot_be_null"), "file_cannot_be_null")
        try:
            emails = parser_excel(file)["email"].values()
        except KeyError as exc:
            raise KeepsBadRequestError(
                _("spreadsheet_not_contains_email_header"), "spreadsheet_not_contains_email_header"
            ) from exc

        founds = User.objects.filter(email__in=emails).values("id", "name", "email", "avatar")
        founded_emails = founds.values_list("email", flat=True)
        not_founds = list(filter(lambda email: email not in founded_emails, emails))
        serializer = self.get_serializer_class()(data={"founds": founds, "not_founds": not_founds})
        serializer.is_valid()
        return Response(data=serializer.data)
