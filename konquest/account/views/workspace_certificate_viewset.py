from account.models import WorkspaceCertificate
from account.serializers.workspace_certificate_serializer import WorkspaceCertificateSerializer
from account.services.workspace_service import WorkspaceService
from authentication.keeps_permissions import ADMIN_PERMISSIONS
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter
from utils.utils import Utils


class WorkspaceCertificateViewSet(viewsets.ModelViewSet):
    permission_classes = ADMIN_PERMISSIONS
    queryset = WorkspaceCertificate.objects.all()
    serializer_class = WorkspaceCertificateSerializer
    filter_backends = [OrderingFilter, SearchFilter]
    ordering_fields = ["language"]
    search_fields = ["language", "certificate_type", "workspace__name", "workspace__id"]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = WorkspaceService()

    def get_workspace_uuid(self):
        return self.request.user.get("client_id")

    def get_queryset(self):
        return super().get_queryset().filter(workspace_id=self.get_workspace_uuid())

    def handle_request_data(self, request, certificate_path=None):
        workspace_uuid = self.get_workspace_uuid()
        request.data["workspace"] = workspace_uuid
        if certificate_path:
            request.data["certificate_path"] = certificate_path

    def handle_certificate_upload(self, request):
        if "jasper_file" in request.data:
            workspace_uuid = self.get_workspace_uuid()
            language = request.data.get("language")
            jasper_file = request.data.get("jasper_file")
            certificate_type = request.data.get("certificate_type")
            jasper_path = Utils.temp_file(jasper_file)
            upload_file = self._service.upload_certificate_template(
                jasper_path, workspace_uuid, language, certificate_type
            )
            return upload_file["name"]

    def create(self, request, *args, **kwargs):
        certificate_path = self.handle_certificate_upload(request)
        self.handle_request_data(request, certificate_path)
        return super().create(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        certificate_path = self.handle_certificate_upload(request)
        self.handle_request_data(request, certificate_path)
        return super().partial_update(request, *args, **kwargs)
