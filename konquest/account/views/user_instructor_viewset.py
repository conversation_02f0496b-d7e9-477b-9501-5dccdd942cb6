from urllib import parse
from urllib.parse import urlencode

from authentication.keeps_permissions import ADMIN_PERMISSIONS
from custom import KeepsError
from injector import Provider, inject
from myaccount.application.services.myaccount_service import MyAccountService
from rest_framework import serializers, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED
from typing_extensions import deprecated
from utils.aws import S3Client


@deprecated("This ViewSet Will be Removed Soon")
class UserInstructorViewSet(viewsets.ViewSet):
    """
    A viewset that provides the standard actions
    """

    class InstructorSerializer(serializers.Serializer):
        name = serializers.CharField()
        email = serializers.EmailField()

        def update(self, instance, validated_data):
            """
            This method is empty because updating instances is not required for this serializer.
            """
            pass

        def create(self, validated_data):
            """
            This method is empty because creation instances is not required for this serializer.
            """
            pass

    filter_backends = (SearchFilter, OrderingFilter)
    serializer_class = InstructorSerializer
    search_fields = ("name",)
    ordering_fields = ("name",)
    ordering = ("name",)

    permission_classes = ADMIN_PERMISSIONS

    @inject
    def __init__(
        self,
        myaccount_service: MyAccountService = Provider[MyAccountService],
        s3_client: S3Client = Provider[S3Client],
        **kwargs
    ):
        super().__init__(**kwargs)
        self._myaccount_service = myaccount_service
        self._s3_client = s3_client

    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        response_data = self._myaccount_service.create_instructor(
            data, self.request.META.get("HTTP_AUTHORIZATION"), self.request.user.get("client_id")
        )
        if not response_data:
            raise KeepsError("unable to register user", "unable_to_register_user", status_code=500)
        data["id"] = response_data["id"]
        return Response(data=data, status=HTTP_201_CREATED)

    def list(self, request, *args, **kwargs):
        response_data = self._myaccount_service.list_users_able_to_be_instructor(
            self.request.query_params, self.request.user.get("client_id")
        )
        if response_data.get("next"):
            response_data["next"] = self._format_pagination_data(response_data["next"])
        if response_data.get("previous"):
            response_data["previous"] = self._format_pagination_data(response_data["previous"])

        return Response(data=response_data)

    def _format_pagination_data(self, pagination_url: str):
        path = self.request.get_full_path()
        if "?" in path:
            path = path.split("?")[0]
        if "?" in pagination_url:
            query = dict(parse.parse_qsl(parse.urlsplit(pagination_url).query))
            if "role_id" in query:
                query.pop("role_id")
            return f"{path}?{urlencode(query)}"
        return f"{path}"
