from authentication.keeps_permissions import KeepsIntegrationPermission
from mission.services.disable_mission_by_group_service import DisableAluraIntegrationService
from mission.services.enable_mission_by_group_service import EnableAluraIntegrationService
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK


class AluraIntegrationViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = (KeepsIntegrationPermission,)

    def disable_integration(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id")
        service = DisableAluraIntegrationService(workspace_id)
        service.execute()
        return Response(status=HTTP_200_OK)

    def enable_integration(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id")
        service = EnableAluraIntegrationService(workspace_id)
        service.execute()
        return Response(status=HTTP_200_OK)
