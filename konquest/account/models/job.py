import uuid

from account.models.workspace import Workspace
from django.db import models


class Job(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Job", max_length=200, null=True, blank=True)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.SET_NULL)

    def __str__(self):
        return self.name

    class Meta:
        app_label = "common"
        verbose_name_plural = "Jobs"
        db_table = "job"


class JobFunction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Job Function", null=True, blank=True)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.SET_NULL)

    def __str__(self):
        return self.name

    class Meta:
        app_label = "common"
        verbose_name_plural = "Job Functions"
        db_table = "job_function"
