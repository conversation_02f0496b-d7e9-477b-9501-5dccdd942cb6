import uuid

from config.languages_map import languages
from django.db import models

PT_BR = "pt-BR"


class User(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(verbose_name="User Name", max_length=200, db_index=True)
    email = models.EmailField(verbose_name="Email", max_length=200, null=True, blank=True, db_index=True)
    email_verified = models.BooleanField(verbose_name="Email Verified", default=False)
    avatar = models.TextField(verbose_name="Avatar", null=True, blank=True)
    status = models.BooleanField(verbose_name="Status", default=True)
    phone = models.CharField(verbose_name="Phone", max_length=20, null=True, blank=True)
    last_access_date = models.DateTimeField(verbose_name="Last Access Date", null=True, blank=True)

    language_id = models.UUIDField(verbose_name="Language Id", null=True)
    country = models.Char<PERSON><PERSON>(verbose_name="Country", max_length=90, null=True, blank=True)
    ein = models.Char<PERSON><PERSON>(verbose_name="Employer Identification Number", max_length=100, null=True, blank=True)
    related_user_leader = models.ForeignKey(
        "self", verbose_name="Related User Leader", null=True, on_delete=models.SET_NULL
    )
    time_zone = models.CharField(
        verbose_name="Time Zone", max_length=200, default="america/sao_paulo", null=True, blank=True
    )

    @property
    def language(self) -> str:
        return languages.get(str(self.language_id))

    def __init__(self, *args, **kwargs):
        self.role = ""
        self.workspace_id = ""
        super().__init__(*args, **kwargs)

    class Meta:
        verbose_name_plural = "Users"
        db_table = "user"

    def __str__(self):
        return self.name
