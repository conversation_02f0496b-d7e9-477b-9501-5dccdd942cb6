from enum import Enum

PORTUGUES_BR = ["pt-BR", "Português(BR)"]
PORTUGUES_PT = ["pt-PT", "Português(PT)"]
ENGLISH = ["en", "English"]
SPANISH = ["es", "Español"]


class LanguageEnum(Enum):
    PORTUGUES_BR = PORTUGUES_BR
    ENGLISH = ENGLISH
    SPANISH = SPANISH
    PORTUGUES_PT = PORTUGUES_PT

    @classmethod
    def choices(cls):
        return tuple((choice.value[0], choice.value[1]) for choice in cls)
