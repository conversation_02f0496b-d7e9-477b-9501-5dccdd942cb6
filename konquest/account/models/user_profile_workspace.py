import uuid

from account.models.job import Job, JobFunction
from account.models.user import User
from account.models.workspace import Workspace
from django.db import models


class UserProfileWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.SET_NULL)
    workspace = models.ForeignKey(Workspace, on_delete=models.SET_NULL)
    job_position = models.ForeignKey(Job, verbose_name="Job", null=True, on_delete=models.SET_NULL)
    job_function = models.ForeignKey(JobFunction, verbose_name="Job Function", on_delete=models.SET_NULL)
    director = models.CharField(verbose_name="Director", max_length=200, null=True, blank=True)
    manager = models.CharField(verbose_name="Manager", max_length=200, null=True, blank=True)
    area_of_activity = models.CharField(verbose_name="Area of activity.", max_length=300, null=True, blank=True)

    class Meta:
        app_label = "common"
        verbose_name_plural = "Users Profiles Workspaces"
        db_table = "user_profile_workspace"
