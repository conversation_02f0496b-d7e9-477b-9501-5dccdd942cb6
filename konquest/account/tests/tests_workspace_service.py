import uuid

import mock
from account.models.workspace import Workspace
from account.services.workspace_service import WorkspaceService
from django.test import TestCase
from model_mommy import mommy


@mock.patch("utils.aws.aws_s3.S3Client.send_file_path")
class WorkspaceServiceTestCase(TestCase):
    def setUp(self):
        self.company = mommy.make(Workspace, id=uuid.uuid4(), name="Workspace 1")

    def test_add_certificate_template(self, send_file_path_mock):
        send_file_path_mock.return_value = {
            "url": "www.s3.com/certificate_template/company_company_id_ct.jasper",
            "name": "certificate_template/company_company_id_ct.jasper",
        }
        response = WorkspaceService().upload_certificate_template("file", self.company.id, "pt-BR", "MISSION")

        self.assertEqual(response["name"], "certificate_template/company_company_id_ct.jasper")
