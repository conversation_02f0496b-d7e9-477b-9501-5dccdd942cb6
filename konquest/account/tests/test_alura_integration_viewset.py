import mock
from account.models import User
from account.models.workspace import Workspace
from config import settings
from django.test import TestCase
from django.urls import reverse
from group.models.group import Group
from group.models.group_mission import GroupMission
from mission.models.mission import Mission
from mission.models.mission_development_status_enum import DON<PERSON>, INACTIVATED_BY_INTEGRATION
from model_mommy import mommy
from rest_framework.test import APIClient


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class AluraIntegrationViewSetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace, alura_integration_active=True)
        self.group = mommy.make(Group, workspace=self.workspace, name=settings.GROUP_NAME_ALURA, is_integration=True)
        self.user = mommy.make(User, id=settings.USER_OWNER_ALURA_INTEGRATION_ID, name=settings.GROUP_NAME_ALURA)
        self.mission = mommy.make(Mission, user_creator=self.user)
        mommy.make(GroupMission, group=self.group, mission=self.mission)

        self.user = mommy.make(User, email="<EMAIL>")

        self.headers = {"HTTP_X_CLIENT": self.workspace.id, "Authorization": settings.KEEPS_SECRET_TOKEN_INTEGRATION}

    def test_disable_enable_alura_integration(self, mock_return, mock_rule):
        url = reverse("disable-alura-integration", args=[self.workspace.id])
        response = self.client.post(url, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.mission.refresh_from_db()
        self.assertEqual(self.mission.development_status, INACTIVATED_BY_INTEGRATION)
        self.group.refresh_from_db()
        self.assertTrue(self.group.deleted)
        url = reverse("enable-alura-integration", args=[self.workspace.id])
        response = self.client.post(url, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.mission.refresh_from_db()
        self.assertEqual(self.mission.development_status, DONE)
        self.group.refresh_from_db()
        self.assertFalse(self.group.deleted)
