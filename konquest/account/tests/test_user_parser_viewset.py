import os
from io import BytesIO

import mock
from account.models import User
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class UserParserViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.user = mommy.make(User, email="<EMAIL>")
        self.user_1 = mommy.make(User, email="<EMAIL>")

        self.headers = {}
        self.url = reverse("users-parser-list")
        self.client.force_authenticate(user={})

    def test_parser_xlsx_to_users(self, mock_return, mock_rule):
        xlsx_path = os.path.join(os.path.dirname(__file__), "user-parser-test.xlsx")
        with open(xlsx_path, "rb") as file:
            payload = {"file": BytesIO(file.read())}
            response = self.client.post(self.url, data=payload, **self.headers, format="multipart").json()
        self.assertEqual(len(response["founds"]), 2)
        self.assertEqual(len(response["not_founds"]), 1)
