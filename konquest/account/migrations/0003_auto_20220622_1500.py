# Generated by Django 2.2 on 2022-06-22 15:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("mission", "0004_auto_20220622_1500"),
        ("notification", "0004_auto_20220622_1500"),
        ("category", "0002_auto_20220622_1500"),
        ("learning_trail", "0003_auto_20220622_1500"),
        ("account", "0002_auto_20220622_1500"),
        ("pulse", "0002_auto_20220622_1500"),
        ("user_activity", "0006_auto_20220622_1500"),
        ("group", "0003_auto_20220622_1500"),
    ]

    operations = [
        migrations.RenameField(model_name="workspacecertificate", old_name="company", new_name="workspace"),
        migrations.AlterUniqueTogether(
            name="workspacecertificate",
            unique_together={("workspace", "language", "certificate_type")},
        ),
        migrations.AlterModelOptions(
            name="workspace",
            options={"verbose_name_plural": "Workspaces"},
        ),
        migrations.AlterModelOptions(
            name="workspacecertificate",
            options={"verbose_name_plural": "Workspace Certificates"},
        ),
        migrations.AlterField(
            model_name="workspace",
            name="name",
            field=models.CharField(max_length=200, verbose_name="Workspace Name"),
        ),
        migrations.AlterField(
            model_name="workspacecertificate",
            name="workspace",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="account.Workspace", verbose_name="Workspace"
            ),
        ),
        migrations.AlterModelTable(
            name="workspace",
            table="workspace",
        ),
        migrations.AlterModelTable(
            name="workspacecertificate",
            table="workspace_certificate",
        ),
    ]
