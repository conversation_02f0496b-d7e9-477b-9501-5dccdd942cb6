# Generated by Django 2.2 on 2022-09-21 12:53

from django.db import connection, migrations, models

from config.languages_map import languages


def normalize_language(*args):
    with connection.cursor() as cursor:
        cursor.execute('SELECT * FROM "user"')
        columns = [col[0] for col in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
    for result in results:
        language_id = None
        for id, value in languages.items():
            if value == result["language"]:
                language_id = id
        if language_id:
            with connection.cursor() as cursor:
                cursor.execute('UPDATE "user" SET language_id = %s WHERE id = %s')


class Migration(migrations.Migration):

    dependencies = [
        ("account", "0004_merge_20220906_2023"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="language_id",
            field=models.UUIDField(null=True, verbose_name="Language Id"),
        ),
        migrations.RunPython(normalize_language),
    ]
