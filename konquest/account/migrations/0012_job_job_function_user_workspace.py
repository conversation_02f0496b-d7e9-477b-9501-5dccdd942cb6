from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('account', '0011_alter_workspacecertificate_language'),
    ]

    operations = [
        migrations.RunSQL("""
            CREATE TABLE job_function (
                id UUID PRIMARY KEY,
                workspace_id UUID NOT NULL,
                name CHAR(200) NULL
            );
        """),
        migrations.RunSQL("""
            CREATE TABLE job (
                id UUID PRIMARY KEY,
                workspace_id UUID NOT NULL,
                name CHAR(200) NULL
            );
        """),
        migrations.RunSQL("""
            CREATE TABLE user_profile_workspace (
                id UUID PRIMARY KEY,
                workspace_id UUID NOT NULL,
                user_id UUID NOT NULL,
                job_position_id UUID REFERENCES job(id),
                job_function_id UUID REFERENCES job_function(id)
            );
        """),
    ]
