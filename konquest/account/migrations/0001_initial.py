# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Company",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Company Name")),
                (
                    "allow_list_public_categories",
                    models.BooleanField(default=True, verbose_name="Allow List Public Categories"),
                ),
                (
                    "allow_list_public_channel",
                    models.BooleanField(default=False, verbose_name="Allow List Public Channel"),
                ),
                (
                    "allow_create_public_channel",
                    models.BooleanField(default=False, verbose_name="Allow Create Public Channel"),
                ),
                ("allow_list_paid_channel", models.<PERSON>oleanField(default=False, verbose_name="Allow List Paid Channel")),
                (
                    "allow_create_paid_channel",
                    models.BooleanField(default=False, verbose_name="Allow Create Paid Channel"),
                ),
                ("need_approve_channel", models.BooleanField(default=True, verbose_name="Need Approve Channel")),
                (
                    "allow_list_public_mission",
                    models.BooleanField(default=False, verbose_name="Allow List Public Mission"),
                ),
                (
                    "allow_create_public_mission",
                    models.BooleanField(default=False, verbose_name="Allow Create Public Mission"),
                ),
                ("allow_list_paid_mission", models.BooleanField(default=False, verbose_name="Allow List Paid Mission")),
                (
                    "allow_create_paid_mission",
                    models.BooleanField(default=False, verbose_name="Allow Create Paid Mission"),
                ),
                ("need_approve_mission", models.BooleanField(default=True, verbose_name="Need Approve Mission")),
                ("status", models.BooleanField(default=True, verbose_name="Status")),
                ("icon_url", models.TextField(blank=True, null=True, verbose_name="Address")),
                ("logo_url", models.TextField(blank=True, null=True, verbose_name="Address")),
                (
                    "min_performance_certificate",
                    models.FloatField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                        verbose_name="Minimum Performance Certificate",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Companies",
                "db_table": "company",
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="User Name")),
                ("email", models.EmailField(blank=True, max_length=200, null=True, verbose_name="Email")),
                ("email_verified", models.BooleanField(default=False, verbose_name="Email Verified")),
                ("avatar", models.TextField(blank=True, null=True, verbose_name="Avatar")),
                ("status", models.BooleanField(default=True, verbose_name="Status")),
                ("phone", models.CharField(blank=True, max_length=20, null=True, verbose_name="Phone")),
                ("last_access_date", models.DateTimeField(blank=True, null=True, verbose_name="Last Access Date")),
                ("language", models.CharField(max_length=6, null=True, verbose_name="Language")),
                ("country", models.CharField(blank=True, max_length=20, null=True, verbose_name="Country")),
                (
                    "ein",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="Employer Identification Number"
                    ),
                ),
                ("job", models.CharField(blank=True, max_length=200, null=True, verbose_name="Job")),
                (
                    "related_user_leader",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="account.User",
                        verbose_name="Related User Leader",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Users",
                "db_table": "user",
            },
        ),
        migrations.CreateModel(
            name="CompanyCertificate",
            fields=[
                ("id", models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                (
                    "language",
                    models.CharField(
                        choices=[("pt-BR", "Português(BR)"), ("en", "English"), ("es", "Español")],
                        max_length=20,
                        verbose_name="Language",
                    ),
                ),
                ("certificate_path", models.TextField(verbose_name="Certificate Path")),
                (
                    "certificate_type",
                    models.CharField(
                        choices=[("MISSION", "MISSION"), ("LEARNING_TRAIL", "LEARNING_TRAIL")],
                        default="MISSION",
                        max_length=20,
                        verbose_name="Certificate Type",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="account.Company", verbose_name="Company"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Companies Certificates",
                "db_table": "company_certificate",
                "unique_together": {("company", "language", "certificate_type")},
            },
        ),
    ]
