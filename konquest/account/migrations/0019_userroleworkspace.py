# Generated by Django 5.0.1 on 2024-02-26 13:21

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0018_alter_workspace_enrollment_goal_duration_days'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserRoleWorkspace',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.user')),
                ('workspace', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.workspace')),
            ],
            options={
                'verbose_name_plural': 'Users role workspace',
                'db_table': 'user_role_workspace',
            },
        ),
    ]
