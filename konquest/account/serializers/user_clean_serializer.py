from typing import List, Optional

from account.models.user import User
from account.models.user_profile_workspace import UserProfileWorkspace
from rest_framework import serializers


class UserCleanSerializer(serializers.ModelSerializer):
    job = serializers.SerializerMethodField()

    @staticmethod
    def get_job(obj: User) -> Optional[str]:
        if not hasattr(obj, "user_profile_workspace"):
            return

        profiles: List[UserProfileWorkspace] = obj.__getattribute__("user_profile_workspace")
        if not profiles:
            return

        profile = profiles[0]
        if profile and profile.job_position and profile.job_position.name:
            return profile.job_position.name.strip()

    class Meta:
        model = User
        fields = "__all__"
