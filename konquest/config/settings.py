"""
Django settings for config project.
"""

import os
import sys

from corsheaders.defaults import default_headers
from distutils import util as distutils

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
from django.utils.log import DEFAULT_LOGGING
from django.utils.translation import gettext_lazy as _
from kombu import Exchange, Queue


def str_to_bool(value):
    return value.lower() in ("true", "1")


BASE_DIR = os.path.dirname(os.path.dirname(__file__))
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")  # staging;production
ELASTIC_APM_ENVIRONMENT = os.getenv("ELASTIC_APM_ENVIRONMENT", "development")

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "5i%ul!b#rui^zipl!+u5nh0+7@j4wtu6o=!0_99t3axph$@oc="

# SECURITY WARNING: don't run with debug turned on in production!
_debug = os.getenv("DEBUG", "True")
DEBUG = True if _debug == "True" or _debug is True else False
_debug_all_queries = os.getenv("DEBUG_ALL_QUERIES", "False")
DEBUG_ALL_QUERIES = True if _debug_all_queries == "True" or _debug_all_queries is True else False

ALLOWED_HOSTS = ["*"]

APPEND_SLASH = False

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Application definition
INSTALLED_APPS = [
    "rest_framework",
    "django_injector",
    "drf_yasg",
    "rules.apps.AutodiscoverRulesConfig",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "drf_multiple_model",
    "account.apps.AccountConfig",
    "achievement.apps.AchievementConfig",
    "goal_mission.apps.GoalMissionConfig",
    "group.apps.GroupConfig",
    "learn_content.apps.LearnContentConfig",
    "mission.apps.MissionConfig",
    "pulse.apps.PulseConfig",
    "notification.apps.NotificationConfig",
    "user_activity.apps.UserActivityConfig",
    "category.apps.CustomCategoriesConfig",
    "learning_trail.apps.LearningTrailConfig",
    "history.apps.HistoryConfig",
    "repository.apps.RepositoryConfig",
    "gamification.apps.GamificationConfig",
    "auditlog",
    "django_celery_beat",
    "tasks.apps.TasksConfig",
    "transfers.apps.TransfersConfig"
]

if ELASTIC_APM_ENVIRONMENT in ["stage", "staging", "production"]:  # or True to enable local APM monitoring
    INSTALLED_APPS.append("elasticapm.contrib.django")

# This scheduler config will:
# - Store jobs in the project database
# - Execute jobs in threads inside the application process

SCHEDULER_AUTOSTART = True

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "config.keeps_middleware.LocaleMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "custom.middlewares.timezone_middleware.TimezoneMiddleware",
    "custom.middlewares.global_requests_middleware.GlobalRequestMiddleware",
    "custom.middlewares.disable_options_method_middleware.DisableOptionsMethodMiddleware",
]

if DEBUG_ALL_QUERIES:
    MIDDLEWARE.append("django_sqlprint_middleware.SqlPrintMiddleware")

ROOT_URLCONF = "config.urls"

INJECTOR_MODULES = [
    "account.module.AccountModule",
    "custom.module.CustomModule",
    "pulse.module.PulseModule",
    "rest_clients.module.RestClientModule",
    "mission.module.MissionModule",
    "learning_trail.module.LearningTrailModule",
    "user_activity.module.UserActivityModule",
    "group.module.GroupModule",
    "notification.module.NotificationModule",
    "utils.module.UtilsModule",
    "repository.module.RepositoryModule",
    "notification.module.NotificationModule",
    "transfers.module.TransfersModule",
    "myaccount.module.MyAccountModule",
    "learn_content.module.LearnContentModule"
]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates/"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

# Database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("DATABASE_NAME", "konquest_test"),
        "USER": os.environ.get("DATABASE_USER", "postgres"),
        "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
        "HOST": os.environ.get("DATABASE_HOST", "localhost"),
        "PORT": os.environ.get("DATABASE_PORT", "5432"),
        # Mantém conexões ativas por 30 segundos para reuso
        # Como o PgBouncer está lidando com pooling, manter conexões ativas por 30 segundos no Django é suficiente.
        "CONN_MAX_AGE": int(os.environ.get("DATABASE_CONN_MAX_AGE", "30")),
    }
}

LOCALE_FILE_JSON = f"{BASE_DIR}/config/locale.json"
EMAIL_LOCALE_PATH = os.path.join(BASE_DIR, "email_locale")
COMPANY_THEME_COLOR_MAP = f"{BASE_DIR}/config/company_theme_color_map.json"

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
LANGUAGE_CODE = "en"
LOCALE_PATHS = (os.path.join(BASE_DIR, "locale"),)

LANGUAGES = [("pt-br", _("Portuguese")), ("es", _("Spanish")), ("en", _("English"))]

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True
# Static files (CSS, JavaScript, Images)

STATIC_URL = "/static/"

STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Rest Framework
REST_FRAMEWORK = {
    "DEFAULT_RENDERER_CLASSES": ("rest_framework.renderers.JSONRenderer",),
    "DEFAULT_PERMISSION_CLASSES": [
        "authentication.keeps_permissions.IsAuthenticatedWithoutXClient",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": ["authentication.keeps_authentication.KeepsAuthentication"],
    "DEFAULT_PAGINATION_CLASS": "config.pagination_config.StandardResultsSetPagination",
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
    "PAGE_SIZE": 100,
}

AUTHENTICATION_BACKENDS = (
    "rules.permissions.ObjectPermissionBackend",
    "django.contrib.auth.backends.ModelBackend",
)

if "test" not in sys.argv:
    REST_FRAMEWORK["EXCEPTION_HANDLER"] = "custom.custom_exception_handler.custom_exception_handler"

# SWAGGER
SWAGGER_SETTINGS = {
    "USE_SESSION_AUTH": False,
    "SECURITY_DEFINITIONS": {},
    "VALIDATOR_URL": "",
    "OPERATIONS_SORTER": "method",
    "TAGS_SORTER": None,
    "DOC_EXPANSION": "list",
    "DEEP_LINKING": False,
    "SHOW_EXTENSIONS": True,
    "DEFAULT_AUTO_SCHEMA_CLASS": "config.docs.SwaggerAutoSchemaCustom",
    "DEFAULT_INFO": "config.docs.api_info",
    "DEFAULT_MODEL_RENDERING": "model",
    "DEFAULT_MODEL_DEPTH": 2,
}

if "nose" in sys.argv:
    TEST_RUNNER = "django_nose.NoseTestSuiteRunner"

    NOSE_ARGS = [
        "--with-coverage",
        "--cover-package=account/*,achievement,goal_mission,group,learn_content,mission,notification,pulse,user_activity,tasks,utils",
    ]

# Keycloak Configurations

KEYCLOAK_PUBLIC_KEY = ""

KEYCLOAK_FORMAT_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
{}
-----END PUBLIC KEY-----"""

KEYCLOAK_SERVER_URL = os.environ.get("KEYCLOAK_SERVER_URL", "https://iam.keepsdev.com/auth/")
KEYCLOAK_REALM = os.environ.get("KEYCLOAK_REALM", "keeps-dev")
KEYCLOAK_CLIENT_ID = os.environ.get("KEYCLOAK_CLIENT_ID", "konquest-microservice")
KEYCLOAK_CLIENT_SECRET_KEY = os.environ.get("KEYCLOAK_CLIENT_SECRET_KEY", "")
KEYCLOAK_CLIENT_PUBLIC_KEY = os.environ.get("KEYCLOAK_CLIENT_PUBLIC_KEY", KEYCLOAK_PUBLIC_KEY)

KEYCLOAK_CONFIG = {
    "KEYCLOAK_SERVER_URL": KEYCLOAK_SERVER_URL,
    "KEYCLOAK_REALM": KEYCLOAK_REALM,
    "KEYCLOAK_CLIENT_ID": KEYCLOAK_CLIENT_ID,
    "KEYCLOAK_CLIENT_SECRET_KEY": KEYCLOAK_CLIENT_SECRET_KEY,
    "KEYCLOAK_CLIENT_PUBLIC_KEY": KEYCLOAK_FORMAT_PUBLIC_KEY.format(KEYCLOAK_CLIENT_PUBLIC_KEY),
}

CORS_ALLOW_HEADERS = default_headers + ("x-client", "traceparent", "tracestate")

CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)

CORS_ORIGIN_ALLOW_ALL = True

CORS_ORIGIN_WHITELIST = (
    "http://localhost:4200",
    "http://127.0.0.1:4200",
    "http://127.0.0.1:8000",
    "http://localhost:4100",
    "http://127.0.0.1:4100",
    "http://kizup-api.keepsdev.com",
    "http://konquest-api.keepsdev.com",
    "http://myaccount-api.keepsdev.com",
    "http://kontent-api.keepsdev.com",
    "http://kizup.keepsdev.com",
    "http://konquest.keepsdev.com",
    "http://myaccount.keepsdev.com",
    "http://account.keepsdev.com",
    "http://kontent.keepsdev.com",
    "http://kizup-api-stage.keepsdev.com",
    "http://konquest-api-stage.keepsdev.com",
    "http://myaccount-api-stage.keepsdev.com",
    "http://kontent-api-stage.keepsdev.com",
    "http://kizup-stage.keepsdev.com",
    "http://konquest-stage.keepsdev.com",
    "http://myaccount-stage.keepsdev.com",
    "http://kontent-stage.keepsdev.com",
    # HTTPS
    "https://kizup-api.keepsdev.com",
    "https://konquest-api.keepsdev.com",
    "https://myaccount-api.keepsdev.com",
    "https://kontent-api.keepsdev.com",
    "https://kizup.keepsdev.com",
    "https://konquest.keepsdev.com",
    "https://myaccount.keepsdev.com",
    "https://account.keepsdev.com",
    "https://kontent.keepsdev.com",
    "https://kizup-api-stage.keepsdev.com",
    "https://konquest-api-stage.keepsdev.com",
    "https://myaccount-api-stage.keepsdev.com",
    "https://kontent-api-stage.keepsdev.com",
    "https://kizup-stage.keepsdev.com",
    "https://konquest-stage.keepsdev.com",
    "https://myaccount-stage.keepsdev.com",
    "https://learning-platform-api-stage.keepsdev.com",
)

NOTIFICATION_API_URL = os.environ.get(
    "NOTIFICATION_API_URL", "https://learning-platform-api-stage.keepsdev.com/notification"
)
MYACCOUNT_API_URL = os.environ.get("MYACCOUNT_API_URL", "https://learning-platform-api-stage.keepsdev.com/myaccount")
MYACCOUNT_V2_API_URL = os.environ.get(
    "MYACCOUNT_V2_API_URL", "https://learning-platform-api-stage.keepsdev.com/myaccount-v2"
)
KONTENT_API_URL = os.environ.get("KONTENT_API_URL", "https://learning-platform-api-stage.keepsdev.com/kontent")
ANALYTICS_API_URL = os.environ.get(
    "ANALYTICS_API_URL", "https://learning-platform-api-stage.keepsdev.com/analytics/api/v1"
)
REGULATORY_COMPLIANCE_API_URL = os.environ.get(
    "REGULATORY_COMPLIANCE_API_URL", "https://learning-platform-api-stage.keepsdev.com/regulatory-compliance"
)
CERTIFICATE_MANAGER_GRPC_URL = os.environ.get(
    "CERTIFICATE_MANAGER_GRPC_URL", "certificate-manager-svc:50051"
)

# Konquest - account
APPLICATION_ID = "0abf08ea-d252-4d7c-ab45-ab3f9135c288"
APPLICATION_NAME = os.environ.get("APPLICATION_NAME", "KONQUEST")
KONQUEST_MYACC_USER_ROLE = os.environ.get("KONQUEST_MYACC_USER_ROLE", "a6d23aea-807e-4374-964e-c725b817742d")
KONQUEST_MYACC_ADMIN_ROLE = os.environ.get("KONQUEST_MYACC_ADMIN_ROLE", "297a88de-c34b-4661-be8a-7090fa9a89e5")
KONQUEST_CONTENT_ROLE = os.environ.get("KONQUEST_CONTENT_ROLE", "97f4a026-f727-4e23-bdf9-971fec7ce20e")
KONQUEST_CURATOR_ROLE = os.environ.get("KONQUEST_CURATOR_ROLE", "45f1fc7f-56f4-4208-a347-ee4d84a8f064")
KONQUEST_SUPER_ADMIN_ROLE = os.environ.get("KONQUEST_SUPER_ADMIN_ROLE", "c2a0da89-311d-4e4f-bf7b-c49d7c15f2b6")
KONQUEST_INSTRUCTOR_ROLE = os.environ.get("KONQUEST_INSTRUCTOR_ROLE", "5f19d9b6-dc84-4db3-9074-8f8dfbbe51c8")

KONQUEST_ROLES = [
    KONQUEST_MYACC_USER_ROLE,
    KONQUEST_MYACC_ADMIN_ROLE,
    KONQUEST_CONTENT_ROLE,
    KONQUEST_CURATOR_ROLE,
    KONQUEST_SUPER_ADMIN_ROLE,
    KONQUEST_INSTRUCTOR_ROLE,
]

ROLE_USER_MYACCOUNT = os.environ.get("ROLE_USER_MYACCOUNT", "3b16b975-0297-4edf-950b-e3700b0d0d01")

DEFAULT_USER_LANGUAGE_ID = os.environ.get("DEFAULT_USER_LANGUAGE_ID", "ea636f50-fdc4-49b0-b2de-9e5905de456b")

LEARNING_TRAIL_OPEN_FOR_COMPANY = "Open For Workspace"
LEARNING_TRAIL_CLOSE_FOR_COMPANY = "Close For Workspace"

REDIS_LOCATION = os.getenv("REDIS_LOCATION")

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.filebased.FileBasedCache",
        "LOCATION": "/var/tmp/django_cache",
    }
}

if ENVIRONMENT in ["stage", "staging", "production"] or REDIS_LOCATION:
    CACHES["default"] = {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_LOCATION,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": os.getenv("REDIS_PASSWORD"),
        },
        "TIMEOUT": 300,
        "KEY_PREFIX": "konquest",
    }


# only active this for tests
SUSPEND_SIGNALS = bool(distutils.strtobool(os.getenv("SUSPEND_SIGNALS", "False")))

AWS_REPORTS_BUCKET_NAME = os.getenv("AWS_REPORTS_BUCKET_NAME", "keeps.reports")
AWS_CERTIFICATE_TEMPLATE_BUCKET_PATH = os.getenv("AWS_CERTIFICATE_TEMPLATE_BUCKET_PATH", "certificate_template")

AWS_BUCKET_NAME = os.getenv("AWS_BUCKET_NAME", "keeps-media-stage")
AWS_BUCKET_PATH = os.getenv("AWS_BUCKET_PATH", "konquest")
AWS_CDN_BASE_URL = os.getenv("AWS_CDN_BASE_URL", "https://media-stage.keepsdev.com")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "********************")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf")
AWS_REGION_NAME = os.getenv("AWS_REGION_NAME", "sa-east-1")
AWS_MAIL_SENDER = os.getenv("AWS_MAIL_SENDER", "Plataforma Aprendizagem <<EMAIL>>")
AWS_REPORTS_S3_BASE_URL = os.getenv("AWS_REPORTS_S3_BASE_URL", "https://s3.amazonaws.com/keeps.reports")

AWS_LAMBDA_ACCESS_KEY_ID = os.getenv("AWS_LAMBDA_ACCESS_KEY_ID")
AWS_LAMBDA_SECRET_ACCESS_KEY = os.getenv("AWS_LAMBDA_SECRET_ACCESS_KEY")
AWS_LAMBDA_REGION_NAME = os.getenv("AWS_LAMBDA_REGION_NAME", "us-east-1")

AWS_SES_REGION_NAME = os.getenv("AWS_LAMBDA_REGION_NAME", "us-east-1")

# Certificate
CERTIFICATE_LOGO_DEFAULT = os.getenv(
    "CERTIFICATE_LOGO_DEFAULT", "https://s3.amazonaws.com/keeps.reports/assets/icones3.png"
)
CERTIFICATE_TEMPLATE_DEFAULT_PATH = os.getenv(
    "CERTIFICATE_TEMPLATE_DEFAULT_PATH", "certificate_template/mission.jasper"
)
MISSION_CERTIFICATE_SUMMARY_TEMPLATE = os.getenv(
    "MISSION_CERTIFICATE_SUMMARY_TEMPLATE", "certificate_template/summary_mission_certificate_template.jasper"
)
TRAIL_CERTIFICATE_SUMMARY_TEPLATES = {
    "pt-BR": os.getenv(
        "PT_BR_TRAIL_CERTIFICATE_SUMMARY_PATH",
        "certificate_template/summary_learning_trail_certificate_template_pt_br.jasper",
    ),
    "pt-PT": os.getenv(
        "PT_BR_TRAIL_CERTIFICATE_SUMMARY_PATH",
        "certificate_template/summary_learning_trail_certificate_template_pt_br.jasper",
    ),
    "es": os.getenv(
        "ES_TRAIL_CERTIFICATE_SUMMARY_PATH",
        "certificate_template/summary_learning_trail_certificate_template_es.jasper",
    ),
    "en": os.getenv(
        "EN_TRAIL_CERTIFICATE_SUMMARY_PATH",
        "certificate_template/summary_learning_trail_certificate_template_en.jasper",
    ),
}


AWS_COMPREHEND_ACCESS_KEY_ID = os.getenv("AWS_COMPREHEND_ACCESS_KEY_ID")
AWS_COMPREHEND_SECRET_ACCESS_KEY = os.getenv("AWS_COMPREHEND_SECRET_ACCESS_KEY")
AWS_COMPREHEND_REGION_NAME = os.getenv("AWS_COMPREHEND_REGION_NAME", "us-east-1")

# Seed: keepsfuturo2020
KEEPS_SECRET_TOKEN_INTEGRATION = os.getenv("KEEPS_SECRET_TOKEN_INTEGRATION", "637a2f9e72daba2ebb03a699c7a4c08d")

# Tiny PNG
TINY_PNG_API_KEY = os.getenv("TINY_PNG_API_KEY", "Bfe6JznGzkDk4GNAmERnjnaYpvxfWa6w")

# Start with Django's default logging configuration
LOGGING = DEFAULT_LOGGING.copy()

# Override the default django.server formatter to remove sql field reference
LOGGING["formatters"]["django.server"] = {
    "format": "[{server_time}] {message}",
    "style": "{",
}

# Add a formatter that safely handles SQL fields
LOGGING["formatters"]["sql_safe"] = {
    "format": "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s",
    "style": "%",
}

# Override any formatters that might reference 'sql' field
if "formatters" in LOGGING:
    for formatter_name, formatter_config in LOGGING["formatters"].items():
        if isinstance(formatter_config, dict) and "format" in formatter_config:
            format_string = formatter_config["format"]
            if "sql" in format_string:
                # Replace with a safe format that doesn't reference sql field
                LOGGING["formatters"][formatter_name] = {
                    "format": "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s",
                    "style": "%",
                }

LOGGING["handlers"]["slack_admins"] = {
    "level": "ERROR",
    "filters": ["require_debug_false"],
    "class": "custom.discord_webhook.DiscordWebhookLogger",
    "formatter": "sql_safe",  # Use the safe formatter
}

LOGGING["loggers"]["django"] = {
    "handlers": ["console", "slack_admins"],
    "level": "INFO",
}

# https://www.elastic.co/guide/en/apm/agent/python/master/django-support.html#django-logging
LOGGING["loggers"]["elasticapm.errors"] = {
    "level": "ERROR",
    "handlers": ["console"],
    "propagate": False,
}
LOGGING["loggers"]["apm"] = {
    "level": "WARNING",
    "handlers": ["elasticapm"],
    "propagate": False,
}
LOGGING["handlers"]["elasticapm"] = {
    "level": "WARNING",
    "class": "elasticapm.contrib.django.handlers.LoggingHandler",
}

# CELERY STUFF
BROKER_URL = os.getenv("BROKER_URL")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", "celery_amqp_backend.AMQPBackend://")
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "America/Sao_Paulo"
CELERY_QUEUE = os.getenv("CELERY_QUEUE", "konquest-stage")
CELERY_NOTIFICATIONS_QUEUE = os.getenv("CELERY_NOTIFICATIONS_QUEUE", "konquest-notifications")
REGULATORY_COMPLIANCE_QUEUE = os.getenv("REGULATORY_COMPLIANCE_QUEUE", "regulatory-compliance-tasks")
NOTIFICATION_MESSAGE_QUEUE = os.getenv("NOTIFICATION_MESSAGE_QUEUE", "bell")
NOTIFICATION_EXCHANGE_TYPE = os.getenv("NOTIFICATION_EXCHANGE_TYPE", "direct")
NOTIFICATION_EXCHANGE = os.getenv("NOTIFICATION_EXCHANGE_TYPE", "notification")
BELL_NOTIFICATION_ROUTING_KEY = os.getenv("BELL_NOTIFICATION_ROUTING_KEY", "bell")

# Whether to store the task return values or not (tombstones).
# If you still want to store errors, just not successful return values, you can set task_store_errors_even_if_ignored.
CELERY_IGNORE_RESULT = bool(distutils.strtobool(os.getenv("CELERY_IGNORE_RESULT", "True")))

# celery queues setup
CELERY_DEFAULT_QUEUE = os.getenv("CELERY_QUEUE", "konquest-stage")
CELERY_DEFAULT_EXCHANGE_TYPE = "topic"
CELERY_DEFAULT_ROUTING_KEY = os.getenv("CELERY_QUEUE", "konquest-stage")
# task rates
NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USERS_TASK_RATE = os.getenv(
    "NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USERS_TASK_RATE", "10/m"
)
NOTIFY_NEW_MISSIONS_BY_WORKSPACE_TASK_RATE = os.getenv("NOTIFY_NEW_MISSIONS_BY_WORKSPACE_TASK_RATE", "10/h")
NOTIFY_NEW_MISSIONS_BY_USER_TASK_RATE = os.getenv("NOTIFY_NEW_MISSIONS_BY_USER_TASK_RATE", "1/s")

CELERY_QUEUES = (
    Queue(CELERY_QUEUE, Exchange(CELERY_QUEUE), routing_key=CELERY_QUEUE),
    Queue(CELERY_NOTIFICATIONS_QUEUE, Exchange(CELERY_NOTIFICATIONS_QUEUE), routing_key=CELERY_NOTIFICATIONS_QUEUE),
)

EXCHANGE_INTEGRATIONS = os.getenv("EXCHANGE_INTEGRATIONS", "integrations")

# CELERY QUEUE INTEGRATIONS
QUEUE_KONQUEST_COMPANY = os.getenv("QUEUE_KONQUEST_COMPANY", "konquest-companies-stage")
QUEUE_SUFFIX_ROUTING_KEY_ERROR = os.getenv("QUEUE_SUFFIX_ROUTING_KEY_ERROR", "-errors")
TEMP_UPLOAD_FOLDER = f"{BASE_DIR}/temp"
TEMP_FOLDER = f"{BASE_DIR}/temp"
SCRIPT_LOG_FOLDER = f"{BASE_DIR}/scripts/logs"
SCRIPT_LOG_FORMAT = os.getenv("SCRIPT_LOG_FORMAT", "%(asctime)s:%(levelname)s:%(message)s")
MEDIA_ROOT = "temp"

# to ignore celery on postman test
ENVIRONMENT_TEST = os.getenv("ENVIRONMENT_TEST", False)

STATIC_URL = "/static/"

KEEPS_COMPANY_ID = "e76b5082-f4fe-4f41-be79-1977840e16a8"

# default data to emails
KONQUEST_CERTIFICATES_WEB_URL = os.getenv(
    "KONQUEST_CERTIFICATES_WEB_URL", "https://konquest-stage.keepsdev.com/settings/certificates-to-approval"
)
KONQUEST_WEB_URL = os.getenv("KONQUEST_WEB_URL", "https://konquest-stage.keepsdev.com")
KONQUEST_CERTIFICATES_WEB_URL_WITH_WORKSPACE = os.getenv(
    "KONQUEST_CERTIFICATES_WEB_URL_WITH_WORKSPACE",
    "{}/{}/settings/certificates-to-approval".format(KONQUEST_WEB_URL, "{}"),
)
KONQUEST_WEB_URL_WITH_WORKSPACE = os.getenv("KONQUEST_WEB_URL_WITH_WORKSPACE", "{}/{}".format(KONQUEST_WEB_URL, "{}"))
KONQUEST_WEB_MISSION_URL = os.getenv("KONQUEST_WEB_MISSION_URL", f"{KONQUEST_WEB_URL}/missions")
KONQUEST_WEB_MISSION_URL_WITH_WORKSPACE = os.getenv(
    "KONQUEST_WEB_MISSION_URL_WITH_WORKSPACE", "{}/{}/missions".format(KONQUEST_WEB_URL, "{}")
)
KONQUEST_WEB_MISSION_DETAIL_URL = os.getenv(
    "KONQUEST_WEB_MISSION_DETAIL_URL", "{}/missions/{}/details".format(KONQUEST_WEB_URL, "{}")
)
KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE = "{}/{}/C/{}".format(KONQUEST_WEB_URL, "{}", "{}")
KONQUEST_WEB_PULSE_DETAIL_URL = os.getenv(
    "KONQUEST_WEB_PULSE_DETAIL_URL", "{0}/{1}/pulse/{2}".format(KONQUEST_WEB_URL, "{0}", "{1}")
)
KONQUEST_WEB_PULSE_DETAIL_URL_WITH_WORKSPACE = os.getenv(
    "KONQUEST_WEB_PULSE_DETAIL_URL_WITH_WORKSPACE", "{}/{}/pulse/{}".format(KONQUEST_WEB_URL, "{}", "{}")
)
KONQUEST_WEB_CHANNEL_DETAIL_URL = os.getenv(
    "KONQUEST_WEB_CHANNEL_DETAIL_URL", "{0}/{1}/channels/details/{2}".format(KONQUEST_WEB_URL, "{0}", "{1}")
)
KONQUEST_WEB_CHANNEL_DETAIL_URL_WITH_WORKSPACE = os.getenv(
    "KONQUEST_WEB_CHANNEL_DETAIL_URL_WITH_WORKSPACE", "{}/{}/channels/detail/{}".format(KONQUEST_WEB_URL, "{}", "{}")
)
KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL = os.getenv(
    "KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL", "{}/{}/T/{}".format(KONQUEST_WEB_URL, "{}", "{}")
)

# default urls webhooks
TEAMS_WEBHOOK_URL = os.getenv(
    "TEAMS_WEBHOOK_URL", "https://learning-platform-api-stage.keepsdev.com/teams/api/notification"
)
SLACK_WEBHOOK_URL = os.getenv(
    "SLACK_WEBHOOK_URL", "https://learning-platform-api-stage.keepsdev.com/slack/api/notification"
)

# log colors
RED = "\033[1;31m"
RESET = "\033[0;0m"
GREEN = "\033[0;32m"

# schedulers
# use https://crontab.guru/ to format crontab
EVERY_DAY_IN_THE_NIGHT = "59 23 * * *"
EVERY_DAY_IN_THE_MIDNIGHT = "1 0 * * *"
EVERY_MONDAY_AT_DAWN = "0 7 * * mon"
EVERY_FIRST_MONTH = "0 7 1 * mon"
EVERY_FIVE_MINUTE = "1 * * * *"

DISABLE_EXPIRED_LEARNING_TRAILS_CRONTAB = os.getenv("DISABLE_EXPIRED_LEARNING_TRAILS_CRONTAB", EVERY_DAY_IN_THE_NIGHT)
EXPIRE_ENROLLMENTS_CRONTAB = os.getenv("EXPIRE_ENROLLMENTS_CRONTAB", EVERY_DAY_IN_THE_NIGHT)
NOTIFY_USER_WITH_ENROLLMENT_EXPIRING_CRONTAB = os.getenv(
    "NOTIFY_USER_WITH_ENROLLMENT_EXPIRING_CRONTAB", EVERY_DAY_IN_THE_MIDNIGHT
)
NOTIFY_USER_WITH_EXPIRED_ENROLLMENT_CRONTAB = os.getenv(
    "NOTIFY_USER_WITH_EXPIRED_ENROLLMENT_CRONTAB", EVERY_DAY_IN_THE_MIDNIGHT
)
NORMALIZE_ACTIVITIES_BIG_TIME_IN_CRONTAB = os.getenv(
    "NORMALIZE_ACTIVITIES_BIG_TIME_IN_CRONTAB", EVERY_DAY_IN_THE_MIDNIGHT
)
FIX_EMPTY_NAMES_CRONTAB = os.getenv("FIX_EMPTY_NAMES_CRONTAB", EVERY_DAY_IN_THE_MIDNIGHT)
NOTIFY_NEW_MISSION_CRONTAB = os.getenv("NOTIFY_NEW_MISSION_CRONTAB", EVERY_MONDAY_AT_DAWN)
NOTIFY_NEW_PULSES_AND_CHANNELS_CRONTAB = os.getenv("NOTIFY_NEW_PULSES_AND_CHANNELS_CRONTAB", EVERY_MONDAY_AT_DAWN)
NOTIFY_ENROLLED_IN_GROUP_MISSIONS_INTERVAL_MINUTES = os.getenv("NOTIFY_ENROLLED_IN_GROUP_MISSIONS_INTERVAL_MINUTES", 30)
UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_PULSE_CONSUME_MINUTES = os.getenv(
    "UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_PULSE_CONSUME_MINUTES", 5
)
UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_QUIZ_ANSWER_MINUTES = os.getenv(
    "UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_QUIZ_ANSWER_MINUTES", 5
)
NOTIFY_MONTHLY_USER_ACTIVITIES_SUMMARY_CRONTAB = os.getenv(
    "NOTIFY_MONTHLY_USER_ACTIVITIES_SUMMARY_CRONTAB", EVERY_FIRST_MONTH
)
GENERATE_MISSIONS_VERTICAL_HOLDER_IMAGES = os.getenv("GENERATE_MISSIONS_VERTICAL_HOLDER_IMAGES", EVERY_DAY_IN_THE_NIGHT)
NOTIFY_LIVE_MISSION_STARTS_SOON = os.getenv("NOTIFY_LIVE_MISSION_STARTS_SOON", EVERY_DAY_IN_THE_MIDNIGHT)
NOTIFY_PRESENTIAL_MISSION_STARTS_SOON = os.getenv("NOTIFY_LIVE_MISSION_STARTS_SOON", EVERY_DAY_IN_THE_MIDNIGHT)
COMPLETE_SYNC_MISSIONS_CRONTAB = os.getenv("COMPLETE_SYNC_MISSIONS_CRONTAB", EVERY_DAY_IN_THE_MIDNIGHT)
NOTIFY_ALL_TRAIL_ENROLLMENTS_EXPIRING_CRONTAB = os.getenv(
    "NOTIFY_ALL_TRAIL_ENROLLMENTS_EXPIRING_CRONTAB", EVERY_DAY_IN_THE_MIDNIGHT
)
NOTIFY_MISSION_TRAIL_DISABLED_TOO_LONG = os.getenv("NOTIFY_MISSION_TRAIL_DISABLED_TOO_LONG", EVERY_DAY_IN_THE_MIDNIGHT)
MISSION_EXTERNAL_POINTS_BY_MINUTE = os.getenv("MISSION_EXTERNAL_POINTS_BY_MINUTE", 1)
MISSION_SYNC_POINTS_BY_MINUTE = os.getenv("MISSION_SYNC_POINTS_BY_MINUTE", 1)

CONTENT_TYPE_QUESTION_NAME = os.getenv("CONTENT_TYPE_QUESTION_NAME", "Question")
CONTENT_TYPE_QUESTION_IMAGE = os.getenv(
    "CONTENT_TYPE_QUESTION_IMAGE",
    "https://s3.amazonaws.com/keeps.konquest.media.prd/assets/pulse-type/cover/quiz.png"
)
CONTENT_TYPE_QUESTION_IMAGE_COVER = os.getenv(
    "CONTENT_TYPE_QUESTION_IMAGE_COVER",
    "https://s3.amazonaws.com/keeps.konquest.media.prd/assets/pulse-type/cover/quiz.png",
)
DEFAULT_WORKSPACE_ICON_URL = os.getenv(
    "DEFAULT_WORKSPACE_ICON_URL",
    "https://media-stage.keepsdev.com/myaccount/workspace-icon/4b5ed54d5c6cb1c676ec4518c7b270.png",
)

# notifications
MISSION_OLDER_DAYS = os.getenv("MISSION_OLDER_DAYS", 30)
PULSE_OLDER_DAYS = os.getenv("PULSE_ODER_DAYS", 30)

DAYS_TO_AUTO_COMPLETE_SYNC_MISSION = int(os.getenv("DAYS_TO_AUTO_COMPLETE_SYNC_MISSION", 7))
LEARN_CONTENT_AGGREGATION_CUTOFF_DAYS = int(os.getenv("LEARN_CONTENT_AGGREGATION_CUTOFF_DAYS", 365))

DISCORD_WEBHOOK = os.getenv(
    "DISCORD_WEBHOOK",
    "https://discord.com/api/webhooks/1015284552610087026/dvJCK-4LHbcSCn1KZpMllwemybjwjW1D2HtZlvolrImfm_sUxPY8iCzbCfCWcJIbztGS",
)


ELASTIC_APM = {
    "SERVICE_NAME": os.getenv("ELASTIC_APM_SERVICE_NAME", "konquest"),
    "SERVER_URL": os.getenv("ELASTIC_APM_SERVER_URL", "https://keeps.apm.us-east-1.aws.cloud.es.io"),
    "SECRET_TOKEN": os.getenv("ELASTIC_APM_SECRET_TOKEN"),
    "ENVIRONMENT": ELASTIC_APM_ENVIRONMENT,
    # "DEBUG": True,  # enable local APM monitoring
    # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
    "TRANSACTION_SAMPLE_RATE": 0.1,  # default: 1.0
    "SPAN_STACK_TRACE_MIN_DURATION": -1,  # default: 5ms
    "SPAN_COMPRESSION_SAME_KIND_MAX_DURATION": "5ms",  # default: 0ms,
    "TRANSACTION_NAME_FROM_ROUTE": bool(distutils.strtobool(os.getenv("TRANSACTION_NAME_FROM_ROUTE", "True"))),
}


DEFAULT_MISSION_VERTICAL_IMAGE = os.getenv(
    "DEFAULT_MISSION_VERTICAL_IMAGE",
    "https://s3.us-east-1.amazonaws.com/keeps.konquest.media.prd/assets/cover/default-vertical-image.jpg",
)

USE_X_FORWARDED_HOST = bool(distutils.strtobool(os.getenv("USE_X_FORWARDED_HOST", "True")))
X_FORWARDED_HOST = os.getenv("X_FORWARDED_HOST", "127.0.0s.1:8000")
FORCE_HTTPS_IN_FORWARDED_HOST = bool(distutils.strtobool(os.getenv("FORCE_HTTPS_IN_FORWARDED_HOST", "False")))

AUDITLOG_INCLUDE_ALL_MODELS = True

AUDITLOG_EXCLUDE_TRACKING_MODELS = (
    "account.user",
    "user_activity.learn_content_activity",
)
AUDITLOG_EXCLUDE_TRACKING_FIELDS = ("created_date", "updated_date", "deleted_date")

NEW_ENROLLMENT_CYCLE_TASK_NAME = os.getenv("NEW_ENROLLMENT_CYCLE_TASK_NAME", "ENROLLMENT_CYCLE.NEW")
REGULATORY_COMPLIANCE_EXCHANGE = os.getenv("REGULATORY_COMPLIANCE_EXCHANGE", "integration")
REGULATORY_COMPLIANCE_EXCHANGE_TYPE = os.getenv("REGULATORY_COMPLIANCE_EXCHANGE_TYPE", "direct")
DELETE_GROUP_USERS_WITHOUT_USER_ROLE_WORKSPACE_INTERVAL_MINUTES = os.getenv(
    "DELETE_GROUP_USERS_WITHOUT_USER_ROLE_WORKSPACE_INTERVAL_MINUTES", 1
)

TOPIC_NAME_USER_ROLE_WORKSPACE = os.getenv(
    "TOPIC_NAME_USER_ROLE_WORKSPACE", "pg_myaccount_stage.public.user_role_workspace"
)

KAFKA_SERVERS = os.getenv("KAFKA_SERVERS")
KAFKA_GROUP_ID = os.getenv("KAFKA_GROUP_ID", "konquest-kafka-worker")

RANKING_PERCENTAGE_MIN_ACTIVATE = os.getenv("RANKING_PERCENTAGE_MIN_ACTIVATE", 70)
RANKING_RULES_CACHE_TIMEOUT = os.getenv("RANKING_RULES_CACHE_TIMEOUT", 60 * 60)

USER_OWNER_ALURA_INTEGRATION_ID = os.getenv("USER_OWNER_ALURA_INTEGRATION_ID", "1b26d935-f87c-4e2a-94e9-e1d377cc5e16")
GROUP_NAME_ALURA = os.getenv("GROUP_NAME_ALURA", "Alura Integration")
