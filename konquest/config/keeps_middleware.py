from django.utils import translation
from django.utils.deprecation import MiddlewareMixin

from utils.utils import get_user_language


class LocaleMiddleware(MiddlewareMixin):
    def process_request(self, request):
        token = request.META.get("HTTP_AUTHORIZATION")
        language = get_user_language(token)
        translation.activate(language)
        request.LANGUAGE_CODE = translation.get_language()

    def process_response(self, request, response):
        translation.deactivate()
        return response
