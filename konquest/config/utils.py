def parse_cron_string(cron_str):
    parts = cron_str.split()
    minute, hour, day_of_month, month, day_of_week = parts[:5]
    day_of_month = "*" if day_of_month == "?" else day_of_month
    month = "*" if month == "?" else month
    day_of_week = "*" if day_of_week == "?" else day_of_week

    return {
        "minute": minute,
        "hour": hour,
        "day_of_month": day_of_month,
        "month_of_year": month,
        "day_of_week": day_of_week,
    }
