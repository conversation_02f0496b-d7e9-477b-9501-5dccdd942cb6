from urllib.parse import urljoin, urlsplit

from django.conf import settings
from django.utils.encoding import iri_to_uri
from django.utils.functional import cached_property
from drf_multiple_model.pagination import MultipleModelLimitOffsetPagination
from rest_framework.pagination import PageNumberPagination
from rest_framework.utils.urls import remove_query_param, replace_query_param

HTTPS_PROTOCOL = "https"


class KeepsPageNumberPagination(PageNumberPagination):
    @cached_property
    def _current_scheme_host(self):
        scheme = HTTPS_PROTOCOL if settings.FORCE_HTTPS_IN_FORWARDED_HOST else self.request.scheme
        if settings.X_FORWARDED_HOST:
            return "{}://{}".format(scheme, settings.X_FORWARDED_HOST)
        return "{}://{}".format(scheme, self.request.get_host())

    def build_absolute_uri(self, location=None):
        if location is None:
            # Make it an absolute url (but schemeless and domainless) for the
            # edge case that the path starts with '//'.
            location = "//%s" % self.request.get_full_path()
        else:
            # Coerce lazy locations.
            location = str(location)
        bits = urlsplit(location)
        if not (bits.scheme and bits.netloc):
            if (
                bits.path.startswith("/")
                and not bits.scheme
                and not bits.netloc
                and "/./" not in bits.path
                and "/../" not in bits.path
            ):
                # If location starts with '//' but has no netloc, reuse the
                # schema and netloc from the current request. Strip the double
                # slashes and continue as if it wasn't specified.
                if location.startswith("//"):
                    location = location[2:]
                location = self._current_scheme_host + location
            else:
                # Join the constructed URL with the provided location, which
                # allows the provided location to apply query strings to the
                # base path.
                location = urljoin(self._current_scheme_host + self.path, location)
        return iri_to_uri(location)

    def get_next_link(self):
        if not self.page.has_next():
            return None
        url = self.build_absolute_uri()
        page_number = self.page.next_page_number()
        return replace_query_param(url, self.page_query_param, page_number)

    def get_previous_link(self):
        if not self.page.has_previous():
            return None
        url = self.build_absolute_uri()
        page_number = self.page.previous_page_number()
        if page_number == 1:
            return remove_query_param(url, self.page_query_param)
        return replace_query_param(url, self.page_query_param, page_number)


class StandardResultsSetPagination(KeepsPageNumberPagination):
    page_size = 100
    page_size_query_param = "per_page"
    max_page_size = 200


class RecommendationResultsSetPagination(PageNumberPagination):
    page_size = 4
    page_size_query_param = "per_page"
    max_page_size = 20


class StandardResultsSetPaginationMultipleModel(MultipleModelLimitOffsetPagination):
    default_limit = 15
    limit_query_param = "per_page"
    max_limit = 100
