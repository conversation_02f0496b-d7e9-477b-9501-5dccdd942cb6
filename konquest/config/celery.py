from __future__ import absolute_import

import os
import re

from celery import Celery
from django.conf import settings
from config.scheduler import beat_schedule

# set the default Django settings module for the 'celery' program.
from config.settings import CELERY_IGNORE_RESULT, CELERY_QUEUE

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
# argument to Celery is name of the current module

app = Celery("konquest")
# Loads configuration from a configuration object
app.config_from_object("django.conf:settings", namespace="CELERY_NAMESPACE")
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)
app.conf.task_default_queue = CELERY_QUEUE
app.conf.task_ignore_result = CELERY_IGNORE_RESULT

# Loading scheduled tasks
app.conf.beat_schedule = beat_schedule
app.conf.task_routes = ([
    (re.compile(r'(?:\w+\.)*notify_.*'), {'queue': settings.CELERY_NOTIFICATIONS_QUEUE}),
    (re.compile(r'(?:\w+\.)*.notifications..*'), {'queue': settings.CELERY_NOTIFICATIONS_QUEUE}),
],)

@app.task(bind=True)
def debug_task(self):
    print("Request: {0!r}".format(self.request))
