from celery.schedules import crontab
from config import settings
from tasks.str_to_crontab import str_to_crontab

beat_schedule = {
    "check_pulse_content_analyzed": {
        "task": "tasks.pulse.check_pulse_content_analyzed",
        "schedule": 20 * 60,  # 20 minutes
    },
    "update_learning_trail_enrollment_progress_by_pulse_consume": {
        "task": "tasks.learning_trail_enrollment.update_learning_trail_enrollment_progress_by_pulse_consume",
        "schedule": settings.UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_PULSE_CONSUME_MINUTES * 60,
    },
    "update_learning_trail_enrollment_progress_by_quiz_answer": {
        "task": "tasks.learning_trail_enrollment.update_learning_trail_enrollment_progress_by_quiz_answer",
        "schedule": settings.UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_QUIZ_ANSWER_MINUTES * 60,
    },
    "check_learning_trail_enrollment_status": {
        "task": "tasks.learning_trail_enrollment.check_learning_trail_enrollment_status",
        "schedule": 10 * 60,  # 10 minutes
    },
    "notify_enrolled_in_group_missions": {
        "task": "tasks.notification.notify_enrolled_in_group_missions",
        "schedule": settings.NOTIFY_ENROLLED_IN_GROUP_MISSIONS_INTERVAL_MINUTES * 60,
    },
    "republish_delayed_missions": {
        "task": "tasks.republish_delayed_missions.republish_delayed_missions",
        "schedule": 5 * 60,  # 5 minutes
    },
    "consume_user_role_workspace": {
        "task": "kafka_worker.consumer_user_role_workspace.consume_user_role_workspace",
        "schedule": settings.DELETE_GROUP_USERS_WITHOUT_USER_ROLE_WORKSPACE_INTERVAL_MINUTES * 60,
    },
    "notify_monthly_user_activities_summary": {
        "task": "tasks.notification.notify_monthly_user_activities_summary",
        "schedule": str_to_crontab(settings.NOTIFY_MONTHLY_USER_ACTIVITIES_SUMMARY_CRONTAB),
    },
    "disable_expired_learning_trails": {
        "task": "tasks.learning_trail.disable_expired_learning_trails",
        "schedule": str_to_crontab(settings.DISABLE_EXPIRED_LEARNING_TRAILS_CRONTAB),
    },
    "expire_enrollments": {
        "task": "tasks.mission_enrollment.expire_enrollments",
        "schedule": str_to_crontab(settings.EXPIRE_ENROLLMENTS_CRONTAB),
    },
    "notify_users_with_expired_mission_enrollment": {
        "task": "tasks.notification.notify_users_with_expired_mission_enrollment",
        "schedule": str_to_crontab(settings.NOTIFY_USER_WITH_EXPIRED_ENROLLMENT_CRONTAB),
    },
    "notify_users_with_mission_enrollment_expiring": {
        "task": "tasks.notification.notify_users_with_mission_enrollment_expiring",
        "schedule": str_to_crontab(settings.NOTIFY_USER_WITH_ENROLLMENT_EXPIRING_CRONTAB),
    },
    "normalize_all_big_time_in": {
        "task": "tasks.learn_content_activity.normalize_all_big_time_in",
        "schedule": str_to_crontab(settings.NORMALIZE_ACTIVITIES_BIG_TIME_IN_CRONTAB),
    },
    "notify_new_missions": {
        "task": "tasks.notification.notify_new_missions",
        "schedule": str_to_crontab(settings.NOTIFY_NEW_MISSION_CRONTAB),
    },
    "notify_new_pulses_and_channels": {
        "task": "tasks.notification.notify_new_pulses_and_channels",
        "schedule": str_to_crontab(settings.NOTIFY_NEW_PULSES_AND_CHANNELS_CRONTAB),
    },
    "notify_live_missions_starts_soon": {
        "task": "tasks.notifications.mission.notify_live_missions_starts_soon",
        "schedule": str_to_crontab(settings.NOTIFY_LIVE_MISSION_STARTS_SOON),
    },
    "notify_presential_missions_starts_soon": {
        "task": "tasks.notifications.mission.notify_presential_missions_starts_soon",
        "schedule": str_to_crontab(settings.NOTIFY_PRESENTIAL_MISSION_STARTS_SOON),
    },
    "complete_sync_missions": {
        "task": "tasks.mission.complete_sync_missions",
        "schedule": str_to_crontab(settings.COMPLETE_SYNC_MISSIONS_CRONTAB),
    },
    "notify_all_trail_enrollments_expiring": {
        "task": "user_activity.tasks.notifications.notify_all_trail_enrollments_expiring",
        "schedule": str_to_crontab(settings.NOTIFY_ALL_TRAIL_ENROLLMENTS_EXPIRING_CRONTAB),
        "args": (5,),
    },
    "notify_mission_trail_disabled_too_long": {
        "task": "learning_trail.tasks.mission_trail_disabled_too_long_notification_task.notify_mission_trail_disabled_too_long",
        "schedule": str_to_crontab(settings.NOTIFY_MISSION_TRAIL_DISABLED_TOO_LONG),
        "args": (3,),
    },
    "clean_duplicated_mission_enrollments": {
        "task": "tasks.mission_enrollment.clean_duplicated_mission_enrollments",
        "schedule": str_to_crontab(settings.EVERY_FIVE_MINUTE),
    },
    "clean_duplicated_trail_enrollments": {
        "task": "tasks.learning_trail_enrollment.clean_duplicated_trail_enrollments",
        "schedule": str_to_crontab(settings.EVERY_FIVE_MINUTE),
    },
    "aggregate_old_learn_content_activity": {
        "task": "tasks.older_activities_aggregation.aggregate_old_learn_content_activity",
        "schedule": str_to_crontab(settings.EVERY_FIRST_MONTH),
    },
}
