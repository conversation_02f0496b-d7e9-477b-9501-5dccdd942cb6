from gamification.models.ranking_workspace import RankingWorkspace
from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from ..dataclass.ranking_activation_errors import RankingActivateResult


class RankingWorkspaceSerializer(serializers.ModelSerializer):
    class Meta:
        model = RankingWorkspace
        fields = ["ranking_general", "ranking_leader", "ranking_director", "ranking_activity_area", "ranking_manager"]


class RankingActivateSerializer(DataclassSerializer):
    class Meta:
        dataclass = RankingActivateResult
