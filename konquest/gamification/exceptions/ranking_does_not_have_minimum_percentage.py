from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

RANKING_DOES_NOT_HAVE_MINIMUM_PERCENTAGE_OF_USERS_FOR_ACTIVATION = gettext_noop(
    "ranking_does_not_have_minimum_percentage_of_users_for_activation"
)


class CategoryNameAlreadyExists(KeepsServiceError):
    def __init__(self):
        super().__init__(
            RANKING_DOES_NOT_HAVE_MINIMUM_PERCENTAGE_OF_USERS_FOR_ACTIVATION,
            _(RANKING_DOES_NOT_HAVE_MINIMUM_PERCENTAGE_OF_USERS_FOR_ACTIVATION),
        )
