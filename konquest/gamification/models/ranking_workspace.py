from account.models.workspace import Workspace
from django.db import models
from utils.models import BaseModel


class RankingWorkspace(BaseModel):
    workspace = models.ForeignKey(
        Workspace, verbose_name="Workspace", on_delete=models.CASCADE, null=False, unique=True
    )
    ranking_general = models.<PERSON>oleanField(verbose_name="General", null=False, default=True)
    ranking_leader = models.BooleanField(verbose_name="Leader", null=False, default=True)
    ranking_director = models.BooleanField(verbose_name="Director", null=False, default=True)
    ranking_activity_area = models.BooleanField(verbose_name="Area", null=False, default=True)
    ranking_manager = models.BooleanField(verbose_name="Manager", null=False, default=True)

    class Meta:
        verbose_name_plural = "Ranking Workspace"
        db_table = "ranking_workspace"
