import uuid

from account.models.user import User
from account.models.workspace import Workspace
from django.db import models
from mission.models.mission import Mission
from pulse.models.pulse import Pulse
from user_activity.models import MissionEnrollment
from user_activity.models.learn_content_activity import LearnContentActivity
from utils.models import BaseModel


class GamificationHistory(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, verbose_name="User", on_delete=models.PROTECT)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.CASCADE, null=True)
    director = models.Char<PERSON>ield(verbose_name="Director", max_length=200, null=True, blank=True)
    manager = models.CharField(verbose_name="Manager", max_length=200, null=True, blank=True)
    area_of_activity = models.CharField(verbose_name="Area of activity.", max_length=300, null=True, blank=True)
    leader = models.Foreign<PERSON><PERSON>(
        User, verbose_name="User", on_delete=models.PROTECT, related_name="user_leader", null=True, blank=True
    )
    mission = models.ForeignKey(Mission, verbose_name="Mission", on_delete=models.PROTECT, null=True, blank=True)
    mission_enrollment = models.ForeignKey(
        MissionEnrollment, verbose_name="Mission Enrollment", on_delete=models.PROTECT, null=True, blank=True
    )
    pulse = models.ForeignKey(Pulse, verbose_name="Pulse", on_delete=models.PROTECT, null=True, blank=True)
    learn_content_activity = models.ForeignKey(
        LearnContentActivity, verbose_name="Learn Content Activity", on_delete=models.PROTECT, null=True, blank=True
    )
    mission_points = models.IntegerField(verbose_name="Mission points", null=True, blank=True)
    pulse_points = models.IntegerField(verbose_name="Pulse points", null=True, blank=True)
    performance = models.FloatField(verbose_name="Performance", null=True, blank=True)

    points_acquired = models.IntegerField(verbose_name="Gamification points acquired", null=True, blank=True)
    acquire_date = models.DateTimeField(verbose_name="Acquire Date", blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["user", "mission", "workspace"], name="constraint_unique_user_mission"),
            models.UniqueConstraint(fields=["user", "pulse", "workspace"], name="constraint_unique_user_pulse"),
        ]
        verbose_name_plural = "Gamification History"
        app_label = "gamification"
        db_table = "gamification_history"
