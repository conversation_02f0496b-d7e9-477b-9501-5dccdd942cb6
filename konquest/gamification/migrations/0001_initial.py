# Generated by Django 5.0.1 on 2024-03-13 18:16

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0020_user_profile_workspace'),
        ('mission', '0026_mission_enrollment_goal_duration'),
        ('pulse', '0007_pulse_is_active'),
        ('user_activity', '0016_auto_20230925_1219'),
    ]

    operations = [
        migrations.CreateModel(
            name='GamificationHistory',
            fields=[
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('deleted_date', models.DateTimeField(null=True, verbose_name='Deleted Date')),
                ('deleted', models.BooleanField(default=False, verbose_name='Deleted')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('director', models.CharField(blank=True, max_length=200, null=True, verbose_name='Director')),
                ('manager', models.CharField(blank=True, max_length=200, null=True, verbose_name='Manager')),
                ('area_of_activity', models.CharField(blank=True, max_length=300, null=True, verbose_name='Area of activity.')),
                ('mission_points', models.IntegerField(blank=True, null=True, verbose_name='Mission points')),
                ('pulse_points', models.IntegerField(blank=True, null=True, verbose_name='Pulse points')),
                ('performance', models.FloatField(blank=True, null=True, verbose_name='Performance')),
                ('points_acquired', models.IntegerField(blank=True, null=True, verbose_name='Gamification points acquired')),
                ('acquire_date', models.DateTimeField(blank=True, null=True, verbose_name='Acquire Date')),
                ('leader', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='user_leader', to='account.user', verbose_name='User')),
                ('learn_content_activity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='user_activity.learncontentactivity', verbose_name='Learn Content Activity')),
                ('mission', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='mission.mission', verbose_name='Mission')),
                ('mission_enrollment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='user_activity.missionenrollment', verbose_name='Mission Enrollment')),
                ('pulse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='pulse.pulse', verbose_name='Pulse')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='account.user', verbose_name='User')),
                ('workspace', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='account.workspace', verbose_name='Workspace')),
            ],
            options={
                'verbose_name_plural': 'Gamification History',
                'db_table': 'gamification_history',
            },
        ),
        migrations.AddConstraint(
            model_name='gamificationhistory',
            constraint=models.UniqueConstraint(fields=('user', 'mission'), name='constraint_unique_user_mission'),
        ),
        migrations.AddConstraint(
            model_name='gamificationhistory',
            constraint=models.UniqueConstraint(fields=('user', 'pulse'), name='constraint_unique_user_pulse'),
        ),
    ]
