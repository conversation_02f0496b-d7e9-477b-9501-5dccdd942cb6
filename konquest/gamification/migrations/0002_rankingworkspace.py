# Generated by Django 5.0.1 on 2024-04-05 11:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0021_alter_user_email_alter_user_name'),
        ('gamification', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RankingWorkspace',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('deleted_date', models.DateTimeField(null=True, verbose_name='Deleted Date')),
                ('deleted', models.BooleanField(default=False, verbose_name='Deleted')),
                ('ranking_general', models.BooleanField(default=True, verbose_name='General')),
                ('ranking_leader', models.<PERSON>oleanField(default=True, verbose_name='Leader')),
                ('ranking_director', models.<PERSON>oleanField(default=True, verbose_name='Director')),
                ('ranking_activity_area', models.BooleanField(default=True, verbose_name='Area')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.workspace', unique=True, verbose_name='Workspace')),
            ],
            options={
                'verbose_name_plural': 'Ranking Workspace',
                'db_table': 'ranking_workspace',
            },
        ),
    ]
