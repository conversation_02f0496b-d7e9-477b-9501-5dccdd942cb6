# Generated by Django 5.0.4 on 2025-01-14 15:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0023_workspace_block_reenrollment'),
        ('gamification', '0004_alter_gamificationhistory_deleted_date_and_more'),
        ('mission', '0034_alter_mission_enrollments_accepted'),
        ('pulse', '0008_alter_channel_deleted_date_and_more'),
        ('user_activity', '0021_missionenrollment_learning_trail_enrollment'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='gamificationhistory',
            name='constraint_unique_user_mission',
        ),
        migrations.RemoveConstraint(
            model_name='gamificationhistory',
            name='constraint_unique_user_pulse',
        ),
        migrations.AddConstraint(
            model_name='gamificationhistory',
            constraint=models.UniqueConstraint(fields=('user', 'mission', 'workspace'), name='constraint_unique_user_mission'),
        ),
        migrations.AddConstraint(
            model_name='gamificationhistory',
            constraint=models.UniqueConstraint(fields=('user', 'pulse', 'workspace'), name='constraint_unique_user_pulse'),
        ),
    ]
