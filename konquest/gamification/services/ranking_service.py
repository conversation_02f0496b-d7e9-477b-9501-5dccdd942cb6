from account.models.user_profile_workspace import UserProfileWorkspace
from account.models.user_role_workspace import UserRoleWorkspace
from config import settings
from django.core.cache import cache
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from gamification.models.ranking_workspace import RankingWorkspace

from ..dataclass.ranking_activation_errors import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RankingActivateResult, RankingError

RANKING_DOES_NOT_HAVE_MINIMUM_PERCENTAGE_OF_USERS_FOR_ACTIVATION = gettext_noop(
    "ranking_does_not_have_minimum_percentage_of_users_for_activation"
)


class RankingService:
    def __init__(self):
        self.ranking_rules = {
            "ranking_general": self.rule_general,
            "ranking_leader": self.rule_leader,
            "ranking_director": self.rule_director,
            "ranking_activity_area": self.rule_activity_area,
            "ranking_manager": self.rule_manager,
        }

    def activate_ranking(self, rankings: dict, workspace_id: str) -> RankingActivateResult:
        rankings_status = self.check_rankings_availability(workspace_id, rankings)
        self._update_ranking(rankings_status, workspace_id)
        return self.format_ranking_errors(rankings_status)

    def check_rankings_availability(self, workspace_id: str, rankings_dict: dict) -> RankingActivateResult:
        rankings_status = {}
        for key, value in rankings_dict.items():
            if value:
                available = self.ranking_rules[key](workspace_id)
                rankings_status[key] = {"state": value, "available": available}
            else:
                rankings_status[key] = {"state": value, "available": True}
        return rankings_status

    def get_ranking_dict(self, ranking: RankingWorkspace):
        return {
            "ranking_general": ranking.ranking_general,
            "ranking_leader": ranking.ranking_leader,
            "ranking_director": ranking.ranking_director,
            "ranking_activity_area": ranking.ranking_activity_area,
            "ranking_manager": ranking.ranking_manager,
        }

    def _update_ranking(self, rankings_status: dict, workspace_id: str) -> None:
        ranking_obj, _ = RankingWorkspace.objects.get_or_create(workspace_id=workspace_id)
        ranking_obj.update(**{key: value["state"] for key, value in rankings_status.items() if value["available"]})
        ranking_obj.save()
        self.set_cache(self.get_ranking_dict(ranking_obj), workspace_id)

    def format_ranking_errors(self, rankings_status: dict) -> RankingActivateResult:
        ranking_errors = [
            RankingError(
                ranking=key,
                error=ErrorDetail(
                    i18n=RANKING_DOES_NOT_HAVE_MINIMUM_PERCENTAGE_OF_USERS_FOR_ACTIVATION,
                    detail=_(RANKING_DOES_NOT_HAVE_MINIMUM_PERCENTAGE_OF_USERS_FOR_ACTIVATION)
                    % {"percent": settings.RANKING_PERCENTAGE_MIN_ACTIVATE, "ranking": _(key)},
                ),
            )
            for key, value in rankings_status.items()
            if not value["available"]
        ]
        return RankingActivateResult(errors=ranking_errors)

    def set_cache(self, rankings: dict, workspace_id) -> None:
        cache_key = f"verify_rankings_{workspace_id}"
        cache.set(cache_key, rankings, timeout=settings.RANKING_RULES_CACHE_TIMEOUT)

    def rule_general(self, workspace_id: str) -> bool:
        return True

    def rule_check(self, workspace_id: str, filter_condition: dict, users_with_condition: int = None) -> bool:
        total_users = self._get_all_users_in_workspace(workspace_id)
        total_count = total_users.count()
        if not users_with_condition:
            users_with_condition = UserProfileWorkspace.objects.filter(
                workspace_id=workspace_id, **filter_condition
            ).count()
        percentage = self.calculate_percentage_of_total(total_count, users_with_condition)
        return percentage >= settings.RANKING_PERCENTAGE_MIN_ACTIVATE

    def rule_leader(self, workspace_id: str) -> bool:
        users_with_condition = UserRoleWorkspace.objects.filter(
            workspace_id=workspace_id, user__related_user_leader__isnull=False
        ).count()
        return self.rule_check(workspace_id, {}, users_with_condition)

    def rule_director(self, workspace_id: str) -> bool:
        return self.rule_check(workspace_id, {"director__isnull": False})

    def rule_activity_area(self, workspace_id: str) -> bool:
        return self.rule_check(workspace_id, {"area_of_activity__isnull": False})

    def rule_manager(self, workspace_id: str) -> bool:
        return self.rule_check(workspace_id, {"manager__isnull": False})

    @staticmethod
    def _get_all_users_in_workspace(workspace_id: str):
        return UserRoleWorkspace.objects.filter(workspace_id=workspace_id).distinct("user")

    @staticmethod
    def calculate_percentage_of_total(total_number: int, partial_number: int) -> float:
        if total_number == 0:
            return 0
        return (partial_number / total_number) * 100

    def verify_rankings_with_cache(self, workspace_id: str) -> bool:
        cache_key = f"verify_rankings_{workspace_id}"
        cache_value = cache.get(cache_key)
        if cache_value:
            return cache_value
        ranking, _ = RankingWorkspace.objects.get_or_create(workspace_id=workspace_id)
        ranking_dict = self.get_ranking_dict(ranking)
        self.activate_ranking(ranking_dict, workspace_id)
        return ranking_dict
