from account.models.user_profile_workspace import UserProfileWorkspace
from django.db.models import (
    <PERSON>v<PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    OuterRef,
    Subquery,
    Sum,
    Value,
    When,
    Window,
)
from django.db.models.functions import Cast, RowNumber
from gamification.models.gamification_history import GamificationHistory
from user_activity.models import MissionEnrollment
from user_activity.models.learn_content_activity import LearnContentActivity
from user_activity.models.learning_trail_enrollment import LearningTrailEnrollment
from user_activity.models.mission_enrollment import COMPLETED


class GamificationService:
    def get_extract_user(self, user_id, workspace_id, start_date, end_date):
        job_position = UserProfileWorkspace.objects.filter(
            user=OuterRef("user"), workspace=OuterRef("workspace")
        ).values("job_position__name")[:1]

        extract = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id, user_id=user_id, acquire_date__date__range=[start_date, end_date]
            )
            .select_related("user")
            .select_related("leader")
            .values(
                "id",
                "user__id",
                "user__name",
                "user__avatar",
                "area_of_activity",
                "director",
                "manager",
                "leader__name",
                "acquire_date",
                "performance",
                "points_acquired",
            )
            .annotate(job_position=Subquery(job_position))
            .annotate(
                total_points=Case(
                    When(mission_id__isnull=False, then=Cast("mission_points", IntegerField())),
                    When(pulse_id__isnull=False, then=Cast("pulse_points", IntegerField())),
                    output_field=CharField(),
                )
            )
            .annotate(
                content=Case(
                    When(mission_id__isnull=False, then=Cast("mission__name", CharField())),
                    When(pulse_id__isnull=False, then=Cast("pulse__name", CharField())),
                    output_field=CharField(),
                )
            )
            .annotate(
                content_type=Case(
                    When(mission_id__isnull=False, then=Value("mission", CharField())),
                    When(pulse_id__isnull=False, then=Value("pulse", CharField())),
                    output_field=CharField(),
                )
            )
            .order_by("-acquire_date")
        )

        return extract

    def get_statistics_user(self, user_id, workspace_id):
        window = {"order_by": [F("points").desc(), F("user__name").asc()]}

        positions = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id, acquire_date__isnull=False, points_acquired__gt=0
            )
            .values("user_id", "user__name", "user__avatar", "area_of_activity", "director", "manager", "leader__name")
            .annotate(points=Sum("points_acquired"))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points", "user__name")
        )
        position_user = None

        for position in positions:
            if str(position.get("user_id")) == str(user_id):
                position_user = position.get("position")
                break

        completed_trails = LearningTrailEnrollment.objects.filter(
            workspace_id=workspace_id, user_id=user_id, status="COMPLETED", deleted=False
        ).count()

        completed_missions = GamificationHistory.objects.filter(
            workspace_id=workspace_id, user_id=user_id, mission_id__isnull=False
        ).count()

        consumed_pulses = GamificationHistory.objects.filter(
            workspace_id=workspace_id, user_id=user_id, pulse_id__isnull=False
        ).count()

        learn_hours = (
            LearnContentActivity.objects.filter(
                workspace_id=workspace_id, user_id=user_id, deleted=False, time_in__isnull=False
            )
            .aggregate(hours=Sum("time_in"))
            .get("hours")
        )
        if learn_hours:
            total_seconds = learn_hours.total_seconds()
            learn_hours = int(total_seconds / 3600)
        learn_hours = learn_hours or 0

        performance_avg = (
            GamificationHistory.objects.filter(workspace_id=workspace_id, user_id=user_id)
            .aggregate(performance=Avg("performance"))
            .get("performance")
        )
        performance_avg = "{}%".format(int((performance_avg or 0) * 100))

        total_enrollments = MissionEnrollment.objects.filter(
            workspace_id=workspace_id, user_id=user_id, deleted=False
        ).count()
        total_enrollments_completed = MissionEnrollment.objects.filter(
            workspace_id=workspace_id, user_id=user_id, deleted=False, status="COMPLETED"
        ).count()

        total_points = (
            GamificationHistory.objects.filter(workspace_id=workspace_id, user_id=user_id)
            .aggregate(total=Sum("points_acquired"))
            .get("total", 0)
        )
        conclusion_rate = 0
        if total_enrollments > 0:
            conclusion_rate = (total_enrollments_completed or 0) / total_enrollments
        conclusion_rate = "{}%".format(int((conclusion_rate) * 100))

        statistics = {}
        statistics["position"] = position_user
        statistics["completed_trails"] = completed_trails
        statistics["completed_missions"] = completed_missions
        statistics["consumed_pulses"] = consumed_pulses
        statistics["learn_hours"] = learn_hours
        statistics["performance_avg"] = performance_avg
        statistics["conclusion_rate"] = conclusion_rate
        statistics["total_points"] = total_points
        return statistics

    def get_general_ranking_gamification(self, workspace_id, start_date, end_date):
        window = {"order_by": [F("points").desc(), F("user__name").asc()]}

        user_profile_info = UserProfileWorkspace.objects.filter(
            user=OuterRef("user"), workspace=OuterRef("workspace")
        ).values("job_position__name", "area_of_activity", "director", "manager")[:1]

        ranking = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id, acquire_date__date__range=[start_date, end_date], points_acquired__gt=0
            )
            .select_related("user")
            .select_related("leader")
            .values("user_id", "user__name", "user__avatar", "user__related_user_leader__name")
            .annotate(points=Sum("points_acquired"))
            .annotate(manager=Subquery(user_profile_info.values("manager")))
            .annotate(director=Subquery(user_profile_info.values("director")))
            .annotate(area_of_activity=Subquery(user_profile_info.values("area_of_activity")))
            .annotate(job_position=Subquery(user_profile_info.values("job_position__name")))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points", "user__name")
        )

        return ranking

    def get_director_ranking_gamification(self, workspace_id, start_date, end_date):
        window = {"order_by": [F("points_avg").desc()]}

        ranking = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id,
                acquire_date__date__range=[start_date, end_date],
                points_acquired__gt=0,
                director__isnull=False,
            )
            .values("director")
            .annotate(num_users=Count("user_id", distinct=True))
            .annotate(points_sum=Sum("points_acquired"))
            .annotate(points_avg=(F("points_sum") / F("num_users")))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points_avg")
        )
        return ranking

    def get_manager_ranking_gamification(self, workspace_id, start_date, end_date):
        window = {"order_by": [F("points_avg").desc()]}

        ranking = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id,
                acquire_date__date__range=[start_date, end_date],
                points_acquired__gt=0,
                manager__isnull=False,
            )
            .values("manager")
            .annotate(num_users=Count("user_id", distinct=True))
            .annotate(points_sum=Sum("points_acquired"))
            .annotate(points_avg=(F("points_sum") / F("num_users")))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points_avg")
        )
        return ranking

    def get_activity_area_ranking_gamification(self, workspace_id, start_date, end_date):
        window = {"order_by": [F("points_avg").desc()]}

        ranking = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id,
                acquire_date__date__range=[start_date, end_date],
                points_acquired__gt=0,
                area_of_activity__isnull=False,
            )
            .values("area_of_activity")
            .annotate(num_users=Count("user_id", distinct=True))
            .annotate(points_sum=Sum("points_acquired"))
            .annotate(points_avg=(F("points_sum") / F("num_users")))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points_avg")
        )
        return ranking

    def get_leader_ranking_gamification(self, workspace_id, start_date, end_date):
        window = {"order_by": [F("points_avg").desc()]}

        ranking = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id,
                acquire_date__date__range=[start_date, end_date],
                points_acquired__gt=0,
                leader__isnull=False,
            )
            .select_related("leader")
            .values("leader__name", "leader__id", "leader__avatar")
            .annotate(num_users=Count("user_id", distinct=True))
            .annotate(points_sum=Sum("points_acquired"))
            .annotate(points_avg=(F("points_sum") / F("num_users")))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points_avg")
        )

        return ranking

    def get_multiplier_ranking_gamification(self, workspace_id, start_date, end_date):
        window = {"order_by": [F("points_avg").desc()]}

        # Usuários que possuem a role conteudista
        ranking = (
            GamificationHistory.objects.filter(
                workspace_id=workspace_id, acquire_date__date__range=[start_date, end_date], points_acquired__gt=0
            )
            .select_related("user")
            .values("user__name")
            .annotate(num_users=Count("user_id", distinct=True))
            .annotate(points_sum=Sum("points_acquired"))
            .annotate(points_avg=(F("points_sum") / F("num_users")))
            .annotate(position=Window(expression=RowNumber(), **window))
            .order_by("-points_avg")
        )

        return ranking

    def create_gamification_history_from_mission(self, mission_enrollment: MissionEnrollment):
        if mission_enrollment.status != COMPLETED:
            return
        user = mission_enrollment.user
        mission = mission_enrollment.mission

        previous_history = GamificationHistory.objects.filter(
            user_id=user.id, mission_id=mission.id, workspace_id=mission_enrollment.workspace_id
        )
        if previous_history.exists():
            return

        user_data = self._get_user_data(user.id, mission_enrollment.workspace_id)

        history, created = GamificationHistory.objects.get_or_create(user=user, mission=mission)
        if created:
            history.workspace_id = mission_enrollment.workspace_id
            history.director = user_data.get("director")
            history.manager = user_data.get("manager")
            history.area_of_activity = user_data.get("area_of_activity")
            history.leader = user_data.get("related_user_leader")
        history.mission_enrollment = mission_enrollment
        history.mission_points = mission.points
        history.performance = mission_enrollment.performance
        history.points_acquired = round((mission.points or 0) * (mission_enrollment.performance or 0))
        history.acquire_date = mission_enrollment.end_date
        history.save()

    def create_gamification_history_from_pulse(self, learn_content_activity: LearnContentActivity):
        if not learn_content_activity.pulse:
            return
        if GamificationHistory.objects.filter(learn_content_activity=learn_content_activity):
            return
        if learn_content_activity.total_pulse_consumption < 0.9:
            return
        user = learn_content_activity.user
        pulse = learn_content_activity.pulse
        if GamificationHistory.objects.filter(user=user, pulse=pulse):
            return

        user_data = self._get_user_data(user.id, learn_content_activity.workspace.id)

        history = GamificationHistory()
        history.user = user
        history.pulse = pulse
        history.workspace = learn_content_activity.workspace
        history.director = user_data.get("director")
        history.manager = user_data.get("manager")
        history.area_of_activity = user_data.get("area_of_activity")
        history.leader = user_data.get("related_user_leader")
        history.learn_content_activity = learn_content_activity
        history.pulse_points = pulse.points
        history.performance = learn_content_activity.total_pulse_consumption
        history.points_acquired = pulse.points
        history.acquire_date = learn_content_activity.time_stop
        history.save()

    def create_history_from_oldest_mission_enrollments(self):
        mission_enrollments = MissionEnrollment.objects.filter(
            ~Exists(GamificationHistory.objects.filter(user=OuterRef("user"), mission_id=OuterRef("mission_id"))),
            deleted=False,
            status="COMPLETED",
        )
        for enrollment in mission_enrollments:
            self.create_gamification_history_from_mission(enrollment)

    def create_history_from_oldest_consumed_pulses(self):
        learn_activities = LearnContentActivity.objects.filter(
            ~Exists(GamificationHistory.objects.filter(user=OuterRef("user"), pulse_id=OuterRef("pulse_id"))),
            deleted=False,
            pulse_id__isnull=False,
            workspace_id__isnull=False,
        )
        for pulse_activity in learn_activities:
            self.create_gamification_history_from_pulse(pulse_activity)

    def _get_user_data(self, user_id, workspace_id):
        data = {}
        try:
            user_profile = UserProfileWorkspace.objects.get(user_id=user_id, workspace_id=workspace_id)

            data["director"] = user_profile.director
            data["manager"] = user_profile.manager
            data["area_of_activity"] = user_profile.area_of_activity
            data["related_user_leader"] = user_profile.user.related_user_leader
        except Exception:
            pass
        return data
