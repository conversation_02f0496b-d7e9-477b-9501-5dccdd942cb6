# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: mission_enrollment.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'mission_enrollment.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18mission_enrollment.proto\x12\x12mission_enrollment\"\xe4\x04\n\x07\x46ilters\x12\x0e\n\x06status\x18\x01 \x03(\t\x12\x18\n\x10performance__gte\x18\x02 \x01(\x02\x12\x18\n\x10performance__lte\x18\x03 \x01(\x02\x12\x17\n\x0fstart_date__gte\x18\x04 \x01(\t\x12\x17\n\x0fstart_date__lte\x18\x05 \x01(\t\x12\x15\n\rend_date__gte\x18\x06 \x01(\t\x12\x15\n\rend_date__lte\x18\x07 \x01(\t\x12\x16\n\x0egoal_date__gte\x18\x08 \x01(\t\x12\x16\n\x0egoal_date__lte\x18\t \x01(\t\x12\x14\n\x0cjob_position\x18\n \x01(\t\x12\x14\n\x0cjob_function\x18\x0b \x01(\t\x12\x10\n\x08\x64irector\x18\x0c \x01(\t\x12\x0f\n\x07manager\x18\r \x01(\t\x12\x18\n\x10\x61rea_of_activity\x18\x0e \x01(\t\x12\x17\n\x0f\x65nd_date__range\x18\x0f \x01(\t\x12\x19\n\x11start_date__range\x18\x10 \x01(\t\x12\x18\n\x10goal_date__range\x18\x11 \x01(\t\x12\n\n\x02id\x18\x12 \x03(\t\x12\x11\n\tdays_late\x18\x13 \x01(\x05\x12\x0f\n\x07mission\x18\x14 \x01(\t\x12\x0c\n\x04user\x18\x15 \x01(\t\x12\x1d\n\x15mission__user_creator\x18\x16 \x01(\t\x12\x12\n\nmission_id\x18\x17 \x01(\t\x12\x10\n\x08\x65nd_date\x18\x18 \x01(\t\x12\x12\n\nstart_date\x18\x19 \x01(\t\x12\x0f\n\x07give_up\x18\x1a \x01(\t\x12\x13\n\x0bperformance\x18\x1b \x01(\t\x12\x11\n\tgoal_date\x18\x1c \x01(\t\"[\n\x07Mission\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\x04name\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x01\x88\x01\x01\x42\x07\n\x05_nameB\x0e\n\x0c_description\"/\n\x04User\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\"\x80\x05\n\x11MissionEnrollment\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x13\n\x0bin_progress\x18\x03 \x01(\x08\x12,\n\x07mission\x18\x04 \x01(\x0b\x32\x1b.mission_enrollment.Mission\x12&\n\x04user\x18\x05 \x01(\x0b\x32\x18.mission_enrollment.User\x12\x11\n\tevaluated\x18\x06 \x01(\x08\x12\x14\n\x0c\x63reated_date\x18\x07 \x01(\t\x12\x14\n\x0cupdated_date\x18\x08 \x01(\t\x12\x14\n\x0c\x64\x65leted_date\x18\t \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\n \x01(\x08\x12\x0e\n\x06points\x18\x0b \x01(\x02\x12\x13\n\x0bperformance\x18\x0c \x01(\x02\x12\x12\n\nstart_date\x18\r \x01(\t\x12\x10\n\x08\x65nd_date\x18\x0e \x01(\t\x12\x11\n\tgoal_date\x18\x0f \x01(\t\x12\x0f\n\x07give_up\x18\x10 \x01(\x08\x12\x17\n\x0fgive_up_comment\x18\x11 \x01(\t\x12\x16\n\x0e\x65nrolled_count\x18\x12 \x01(\x05\x12\x10\n\x08required\x18\x13 \x01(\x08\x12\x11\n\tnormative\x18\x14 \x01(\x08\x12\x10\n\x08progress\x18\x15 \x01(\x02\x12\x17\n\x0f\x63\x65rtificate_url\x18\x16 \x01(\t\x12\x17\n\x0f\x61ssessment_type\x18\x17 \x01(\t\x12 \n\x18\x63\x65rtificate_provider_url\x18\x18 \x01(\t\x12\x13\n\x0b\x61pprove_msg\x18\x19 \x01(\t\x12\x1f\n\x17total_mission_questions\x18\x1a \x01(\x05\x12\x1d\n\x15total_correct_answers\x18\x1b \x01(\x05\"H\n\x18MissionEnrollmentRequest\x12,\n\x07\x66ilters\x18\x02 \x01(\x0b\x32\x1b.mission_enrollment.Filters\"U\n\x1dMissionEnrollmentListResponse\x12\x34\n\x05items\x18\x01 \x03(\x0b\x32%.mission_enrollment.MissionEnrollment2\x82\x01\n\x18MissionEnrollmentService\x12\x66\n\x03Get\x12,.mission_enrollment.MissionEnrollmentRequest\x1a\x31.mission_enrollment.MissionEnrollmentListResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mission_enrollment_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_FILTERS']._serialized_start=49
  _globals['_FILTERS']._serialized_end=661
  _globals['_MISSION']._serialized_start=663
  _globals['_MISSION']._serialized_end=754
  _globals['_USER']._serialized_start=756
  _globals['_USER']._serialized_end=803
  _globals['_MISSIONENROLLMENT']._serialized_start=806
  _globals['_MISSIONENROLLMENT']._serialized_end=1446
  _globals['_MISSIONENROLLMENTREQUEST']._serialized_start=1448
  _globals['_MISSIONENROLLMENTREQUEST']._serialized_end=1520
  _globals['_MISSIONENROLLMENTLISTRESPONSE']._serialized_start=1522
  _globals['_MISSIONENROLLMENTLISTRESPONSE']._serialized_end=1607
  _globals['_MISSIONENROLLMENTSERVICE']._serialized_start=1610
  _globals['_MISSIONENROLLMENTSERVICE']._serialized_end=1740
# @@protoc_insertion_point(module_scope)
