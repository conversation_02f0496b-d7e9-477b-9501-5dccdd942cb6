from google.protobuf.json_format import MessageToDict


def message_to_orm_filters(filters) -> dict:
    filters = MessageToDict(filters, preserving_proto_field_name=True)
    filter_data = {}
    for field_name in filters:
        filter_value = filters[field_name]
        if filter_value is not None and filter_value != "":
            if isinstance(filter_value, list):
                filter_data[field_name] = ",".join(map(str, filter_value))
            else:
                filter_data[field_name] = filter_value
    return filter_data
