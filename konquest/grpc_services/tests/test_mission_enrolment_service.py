from datetime import timed<PERSON><PERSON>
from unittest import mock

from account.models import User, Workspace
from django.test import TestCase
from django.utils import timezone
from grpc_services.generated import mission_enrollment_pb2
from grpc_services.servicers.mission_enrollment_service import MissionEnrollmentServiceServicer
from mission.models import Mission
from model_mommy import mommy
from user_activity.models import MissionEnrollment


class TestMissionEnrollmentGrpcService(TestCase):
    def setUp(self):
        self.service = MissionEnrollmentServiceServicer()
        self.workspace = mommy.make(Workspace)
        self.context = mock.MagicMock()

        self.user = mommy.make(User, name="Test User")
        self.mission = mommy.make(Mission, name="Test Mission")
        self.mission_2 = mommy.make(Mission, name="Another Mission")

        self.enrollment_started = mommy.make(
            MissionEnrollment,
            status="STARTED",
            performance=0,
            workspace_id=self.workspace.id,
            mission=self.mission,
            user=self.user,
        )
        self.enrollment_completed = mommy.make(
            MissionEnrollment,
            status="COMPLETED",
            performance=1,
            workspace_id=self.workspace.id,
            mission=self.mission_2,
            user=self.user,
        )

        self.context.invocation_metadata.return_value = [("workspace_id", self.workspace.id)]

    def test_status_filter(self):
        self.not_included_enrollment = mommy.make(
            MissionEnrollment,
            status="EXPIRED",
            performance=3.5,
            workspace_id=self.workspace.id,
            mission=self.mission_2,
            user=self.user,
        )
        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(status=["STARTED", "COMPLETED"])
        )

        response = self.service.Get(request, self.context)

        self.assertEqual(len(response.items), 2)

    def test_performance_filter(self):
        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(performance__gte=0, performance__lte=0.5)
        )

        response = self.service.Get(request, self.context)

        self.assertEqual(len(response.items), 1)
        self.assertEqual(response.items[0].performance, 0)

    def test_start_date_range_filter(self):
        today = timezone.now()
        start_date = today + timedelta(days=30)
        enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace)
        enrollment.start_date = start_date
        enrollment.save()
        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(start_date__gte=str(start_date.date()))
        )

        response = self.service.Get(request, self.context)

        self.assertEqual(len(response.items), 1)
        self.assertEqual(response.items[0].id, str(enrollment.id))

    def test_end_date_range_filter(self):
        end_date = timezone.now()

        enrollment = mommy.make(
            MissionEnrollment, end_date=end_date, mission=self.mission, user=self.user, workspace=self.workspace
        )

        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(end_date__gte=str(end_date.date()))
        )

        response = self.service.Get(request, self.context)

        self.assertEqual(len(response.items), 1)
        self.assertEqual(response.items[0].id, str(enrollment.id))

    def test_goal_date_range_filter(self):
        goal_date = timezone.now().date() - timedelta(days=100)

        enrollment = mommy.make(
            MissionEnrollment, goal_date=goal_date, mission=self.mission, user=self.user, workspace=self.workspace
        )

        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(goal_date__lte=str(goal_date))
        )

        response = self.service.Get(request, self.context)

        self.assertEqual(len(response.items), 1)
        self.assertEqual(response.items[0].id, str(enrollment.id))

    def test_days_late_filter(self):
        days_late = 10
        today = timezone.now().date()
        goal_date = today - timedelta(days=days_late)

        enrollment = mommy.make(
            MissionEnrollment, goal_date=goal_date, mission=self.mission, user=self.user, workspace=self.workspace
        )

        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(days_late=days_late)
        )

        response = self.service.Get(request, self.context)

        self.assertEqual(len(response.items), 1)
        self.assertEqual(response.items[0].id, str(enrollment.id))

    def test_id_filter(self):
        id = self.enrollment_started.id
        request = mission_enrollment_pb2.MissionEnrollmentRequest(filters=mission_enrollment_pb2.Filters(id=[str(id)]))

        response = self.service.Get(request, self.context)

        assert len(response.items) == 1
        assert response.items[0].id == str(self.enrollment_started.id)

    def test_get_mission_enrollment(self):
        request = mission_enrollment_pb2.MissionEnrollmentRequest(
            filters=mission_enrollment_pb2.Filters(
                status=["STARTED", "COMPLETED"], performance__gte=0, performance__lte=1
            )
        )

        response = self.service.Get(request, self.context)

        assert len(response.items) == 2
        assert response.items[0].id == str(self.enrollment_started.id)
        assert response.items[0].mission.name == self.enrollment_started.mission.name
        assert response.items[0].user.name == self.enrollment_started.user.name
        assert response.items[1].mission.name == self.enrollment_completed.mission.name
        assert response.items[1].user.name == self.enrollment_completed.user.name
