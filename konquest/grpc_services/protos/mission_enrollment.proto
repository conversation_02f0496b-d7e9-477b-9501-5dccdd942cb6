syntax = "proto3";

package mission_enrollment;

message Filters {
  repeated string status = 1;
  float performance__gte = 2;
  float performance__lte = 3;
  string start_date__gte = 4;
  string start_date__lte = 5;
  string end_date__gte = 6;
  string end_date__lte = 7;
  string goal_date__gte = 8;
  string goal_date__lte = 9;
  string job_position = 10;
  string job_function = 11;
  string director = 12;
  string manager = 13;
  string area_of_activity = 14;
  string end_date__range = 15;
  string start_date__range = 16;
  string goal_date__range = 17;
  repeated string id = 18;
  int32 days_late = 19;
  string mission = 20;
  string user = 21;
  string mission__user_creator = 22;
  string mission_id = 23;
  string end_date = 24;
  string start_date = 25;
  string give_up = 26;
  string performance = 27;
  string goal_date = 28;
}

message Mission {
  string id = 1;
  optional string name = 2;
  optional string description = 3;
}

message User {
  string id = 1;
  string name = 2;
  string email = 3;
}

message MissionEnrollment {
  string id = 1;
  string status = 2;
  bool in_progress = 3;
  Mission mission = 4;
  User user = 5;
  bool evaluated = 6;
  string created_date = 7;
  string updated_date = 8;
  string deleted_date = 9;
  bool deleted = 10;
  float points = 11;
  float performance = 12;
  string start_date = 13;
  string end_date = 14;
  string goal_date = 15;
  bool give_up = 16;
  string give_up_comment = 17;
  int32 enrolled_count = 18;
  bool required = 19;
  bool normative = 20;
  float progress = 21;
  string certificate_url = 22;
  string assessment_type = 23;
  string certificate_provider_url = 24;
  string approve_msg = 25;
  int32 total_mission_questions = 26;
  int32 total_correct_answers = 27;
}

message MissionEnrollmentRequest {
  Filters filters = 2;
}

message MissionEnrollmentListResponse {
  repeated MissionEnrollment items = 1;
}

service MissionEnrollmentService {
  rpc Get (MissionEnrollmentRequest) returns (MissionEnrollmentListResponse);
}
