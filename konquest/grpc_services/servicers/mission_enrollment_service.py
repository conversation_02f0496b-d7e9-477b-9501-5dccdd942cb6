from grpc_services.generated import mission_enrollment_pb2
from grpc_services.servicers.generic_service_servicer import GenericServiceServicer
from user_activity.serializers.mission_enrollment_serializer import MissionEnrollmentListSerializer
from user_activity.utils import get_mission_enrollment_list_queryset
from user_activity.views.filters.user_activity_filters import MissionEnrollmentFilter


class MissionEnrollmentServiceServicer(GenericServiceServicer):
    def __init__(self):
        super().__init__(
            message_pb2=mission_enrollment_pb2,
            filters_class=MissionEnrollmentFilter,
            serializer_class=MissionEnrollmentListSerializer,
            response_message_type=mission_enrollment_pb2.MissionEnrollmentListResponse,
            response_message_entity_type=mission_enrollment_pb2.MissionEnrollment,
        )

    def get_queryset(self, workspace_id: str):
        return get_mission_enrollment_list_queryset(workspace_id)
