from unittest import mock

from django.test import TestCase
from django.urls import reverse
from group.models import <PERSON>Channel, GroupUser
from model_mommy import mommy
from pulse.models import Channel, ChannelTypeEnum
from rest_framework.test import APIClient

MOCK_NOTIFY_CHANNEL_LINKED_IN_A_GROUP = "group.tasks.notifications.notify_new_channel_linked_in_group.delay"
MOCK_NOTIFY_USER_LINKED_IN_A_GROUP = "group.tasks.notifications.notify_user_linked_in_a_new_group.delay"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class GroupFilterChannelViewsetTestCase(TestCase):
    """
    Test case to check if Channel must be listed correctly.
    """

    fixtures = ["channel_type", "channel_category", "user", "workspace", "channel", "group", "channel"]

    def setUp(self):
        self.client = APIClient()

        self.user_creator_id = "00e70a6e-f2c6-435d-91e5-f9496d6c99c2"

        self.user_keeps_id = "5f1f2d49-e408-4f46-a6d7-cee808c22e22"
        self.user_bayer_id = "6d3beafd-6d1d-4135-a74e-3538a0bcd28d"
        self.user_all_id = "4f70d72b-d5ab-4293-93d0-c657d8b718b3"

        self.workspace_keeps_id = "a6f33710-c07b-4051-9a80-c30ac8f6ebf1"
        self.workspace_bayer_id = "d27b7876-9936-4529-b394-f7df10fe5899"

        self.keeps_channel_a_id = "66f18647-498c-49fd-beb0-3750ec31dfb5"

        self.keeps_channel_b_id = "8e815688-673a-4b44-a144-26f056027471"

        self.keeps_channel_c_id = "ce23041c-0227-4c69-a720-f04277732bb6"

        self.bayer_channel_a_id = "c1974304-a5dd-4e7f-ba1c-54273546186e"

        self.bayer_channel_b_id = "4ee91cc8-a2a8-4d0e-a760-ddeb1cfafa16"

        self.keeps_g1_id = "0d4c3ab9-4115-4f60-92d4-e23354f4eaec"
        self.keeps_g2_id = "d6108fe7-9e93-4092-b6f1-dddb7c2e808e"
        self.bayer_g1_id = "bf926b0d-5e06-4c7e-9d60-a63e538ee72d"

        self.bayer_channel_b = Channel.objects.get(id=self.bayer_channel_b_id)

        mommy.make(GroupChannel, channel_id=self.keeps_channel_a_id, group_id=self.keeps_g1_id)
        mommy.make(GroupChannel, channel_id=self.keeps_channel_a_id, group_id=self.keeps_g2_id)
        mommy.make(GroupChannel, channel_id=self.keeps_channel_b_id, group_id=self.keeps_g2_id)
        mommy.make(GroupChannel, channel_id=self.bayer_channel_a_id, group_id=self.bayer_g1_id)

        mommy.make(GroupUser, user_id=self.user_keeps_id, group_id=self.keeps_g1_id)
        mommy.make(GroupUser, user_id=self.user_all_id, group_id=self.keeps_g2_id)
        mommy.make(GroupUser, user_id=self.user_all_id, group_id=self.bayer_g1_id)

        self.url = reverse("channels-list")

    def test_mission_workspace_keeps_user_keeps(self, mock_return,mock_return_roles):
        """
        user_keeps --> keeps_g1 (group 1)
        keeps_channel_a --> keeps_g1 (group 1)

        user_keeps should get keeps_channel_c (open for workspace) and keeps_channel_a (close).
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_keeps_id, "client_id": self.workspace_keeps_id})

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 9)
        channels_ids = [result["id"] for result in results]

        # channel open
        self.assertIn(self.keeps_channel_c_id, channels_ids)

        # channel close linked to group 1
        self.assertIn(self.keeps_channel_a_id, channels_ids)

    def test_mission_workspace_bayer_user_bayer(self, mock_return, mock_return_roles):
        """
        user_bayer no group linked

        user_bayer should get only bayer_channel_b (open for workspace)
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(user={"sub": self.user_bayer_id, "client_id": self.workspace_bayer_id})

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]

        self.assertEqual(len(results), 14)
        for result in results:
            self.assertIsNot(result["channel_type"]["id"], ChannelTypeEnum.CLOSE_FOR_COMPANY)

    def test_mission_workspace_keeps_user_full(self, mock_return,mock_return_roles):
        """
        user_all ---> keeps_g2 (group 2 keeps)
        user_all ---> bayer_g1 (group 1 bayer)

        keeps_g2 ---> keeps_channel_a
        keeps_g2 ---> keeps_channel_b

        bayer_g1 ---> bayer_channel_a


        user_all logged with workspace_keeps should get keeps_channel_a (close), keeps_channel_b (close),
        keeps_channel_c (open)

        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_all_id, "client_id": self.workspace_keeps_id})

        response = self.client.get(self.url, **self.headers, format="json").json()
        print(response)
        self.assertEqual(len(response["results"]), 9)
        results = response["results"]
        channel_ids = [result["id"] for result in results]

        # channel (a) close linked to group 2
        self.assertIn(self.keeps_channel_a_id, channel_ids)

        # channel (b) close linked to group 2
        self.assertIn(self.keeps_channel_b_id, channel_ids)

        # channel (c) open
        self.assertIn(self.keeps_channel_c_id, channel_ids)

    def test_mission_workspace_bayer_user_full(self, mock_return,mock_return_roles):
        """
        user_all ---> keeps_g2 (group 2 keeps)
        user_all ---> bayer_g1 (group 1 bayer)

        keeps_g2 ---> keeps_channel_a
        keeps_g2 ---> keeps_channel_b

        bayer_g1 ---> bayer_channel_a


        user_all logged with workspace_bayer should get bayer_channel_a (close) and bayer_channel_b (open)

        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(user={"sub": self.user_all_id, "client_id": self.workspace_bayer_id})

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 15)
        channel_ids = [result["id"] for result in results]

        # channel (a) close
        self.assertIn(self.bayer_channel_a_id, channel_ids)

        # channel (b) open
        self.assertIn(self.bayer_channel_b_id, channel_ids)
