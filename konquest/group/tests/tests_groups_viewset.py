import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from group.models import Group, GroupChannel, GroupMission, GroupUser
from mission.models import Mission
from model_mommy import mommy
from pulse.models import Channel
from rest_framework.test import APIClient


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class GroupViewsetTestCase(TestCase):
    fixtures = [
        "workspace",
        "group",
        "user",
        "mission_type",
        "mission_category",
        "mission",
        "channel_type",
        "channel_category",
        "channel",
    ]

    def setUp(self):
        self.client = APIClient()

        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace_2_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a30"

        self.group_id = "0ac0b202-f41b-49c8-ad9a-4ed6e150fb6b"
        self.group_2_id = "799a2ca5-4cbb-492e-8409-f8b3acf3338f"

        self.group = Group.objects.get(id=self.group_id)
        self.group2 = Group.objects.get(id=self.group_2_id)

        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.mission = mommy.make(Mission, id=uuid.uuid4())
        self.channel = mommy.make(Channel, id=uuid.uuid4())

        mommy.make(GroupUser, user_id=self.user_id, group_id=self.group_id)
        mommy.make(GroupMission, mission=self.mission, group_id=self.group_id)
        mommy.make(GroupChannel, channel=self.channel, group_id=self.group_id)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.headers2 = {"HTTP_X_CLIENT": self.workspace_2_id}
        self.url = reverse("groups-list")

    def test_group_list_workspace_1(self, mock_return, mock_role):
        """
        List groups for workspace 1
        """
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 3)
        self.assertEqual(response["results"][0]["name"], self.group.name)
        self.assertEqual(response["results"][0]["workspace"], self.workspace_id)
        self.assertNotEqual(response["results"][0]["workspace"], self.workspace_2_id)

    def test_group_list_workspace_2(self, mock_return, mock_role):
        """
        List groups for workspace 2
        """
        response = self.client.get(self.url, **self.headers2, format="json").json()
        print(response)
        self.assertEqual(len(response["results"]), 3)
        self.assertEqual(response["results"][0]["name"], self.group2.name)
        self.assertEqual(response["results"][0]["workspace"], self.workspace_2_id)
        self.assertNotEqual(response["results"][0]["workspace"], self.workspace_id)

    def test_group_list_search_name(self, mock_return, mock_role):
        # create other group must not be found
        mommy.make(Group, id=uuid.uuid4(), workspace_id=self.workspace_id, name="Tst")

        response = self.client.get(self.url + "?search=First Empty Group", **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["name"], self.group.name)
        self.assertEqual(response["results"][0]["workspace"], self.workspace_id)

    def test_group_get_success(self, mock_return, mock_role):
        response = self.client.get(reverse("groups-detail", args=[self.group_id]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["name"], self.group.name)
        self.assertEqual(response.status_code, 200)

    def test_group_get_not_allowed(self, mock_return, mock_role):
        """
        Not allowed get group of Workspace 2 logged with Workspace 1
        """
        response = self.client.get(reverse("groups-detail", args=[str(self.group2.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_group_get_not_found(self, mock_return, mock_role):
        response = self.client.get(reverse("groups-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_group_post_success(self, mock_return, mock_role):
        data = {"name": "New Groups", "description": "Test  Group 2"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_group_post_required_field(self, mock_return, mock_role):
        data = {
            "description": "Test  Group 2",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["name"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_group_post_equal_name_not_allowed(self, mock_return, mock_role):
        data = {"name": "First Empty Group", "description": "Test equal name"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 400)

    def test_group_patch_success(self, mock_return, mock_role):
        data = {"name": "Test Group Partial Update"}

        response = self.client.patch(
            reverse("groups-detail", args=[self.group_id]), **self.headers, data=data, format="json"
        )
        response_json = response.json()
        print(response_json)
        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_group_is_integration_cannot_patch(self, mock_return, mock_role):
        self.group.is_integration = True
        self.group.save()
        data = {"name": "Test Group Partial Update"}

        response = self.client.patch(
            reverse("groups-detail", args=[self.group_id]), **self.headers, data=data, format="json"
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["i18n"], "group_integration_cannot_be_updated")

    def test_group_put_success(self, mock_return, mock_role):
        data = {"name": "Test Group 3", "description": "Test  Group 3"}

        response = self.client.put(
            reverse("groups-detail", args=[self.group_id]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_group_put_not_found(self, mock_return, mock_role):
        data = {"name": "Test Group 3", "description": "Test  Group 3"}

        response = self.client.put(
            reverse("groups-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_group_put_not_allowed(self, mock_return, mock_role):
        """
        Not allowed update group of Workspace 2 logged with Workspace 1
        """
        data = {
            "name": "try update name",
        }

        response = self.client.patch(
            reverse("groups-detail", args=[str(self.group2.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 404)

    def test_group_delete_success(self, mock_return, mock_role):
        response = self.client.delete(reverse("groups-detail", args=[self.group_id]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_group_delete_not_found(self, mock_return, mock_role):
        response = self.client.delete(reverse("groups-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_group_list_filtering_by_mission(self, mock_return, mock_role):
        """
        List groups for workspace 1
        """
        _mission_2 = mommy.make(Mission, id=uuid.uuid4())
        _mission_3 = mommy.make(Mission, id=uuid.uuid4())
        _group_2 = mommy.make(Group, id=uuid.uuid4(), workspace_id=self.workspace_id, name="Group Keeps 2")
        _group_3 = mommy.make(Group, id=uuid.uuid4(), workspace_id=self.workspace_id, name="Group Keeps 3")

        mommy.make(GroupMission, mission=_mission_2, group=_group_2)
        mommy.make(GroupMission, mission=_mission_2, group=_group_3)

        response = self.client.get(self.url + "?mission_id=" + str(_mission_2.id), **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)
        self.assertEqual(response["results"][0]["name"], _group_2.name)
        self.assertEqual(response["results"][1]["name"], _group_3.name)

        response = self.client.get(
            self.url + "?mission_id=" + str(self.mission.id), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

        response = self.client.get(self.url + "?mission_id=" + str(_mission_3.id), **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 0)

    def test_group_list_filtering_by_channel(self, mock_return, mock_role):
        """ """
        _channel_2 = mommy.make(Channel, id=uuid.uuid4())
        _channel_3 = mommy.make(Channel, id=uuid.uuid4())
        _group_2 = mommy.make(Group, id=uuid.uuid4(), workspace_id=self.workspace_id, name="Group Keeps 2")
        _group_3 = mommy.make(Group, id=uuid.uuid4(), workspace_id=self.workspace_id, name="Group Keeps 3")

        mommy.make(GroupChannel, channel=_channel_2, group=_group_2)
        mommy.make(GroupChannel, channel=_channel_2, group=_group_3)

        response = self.client.get(self.url + "?channel_id=" + str(_channel_2.id), **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)
        self.assertEqual(response["results"][0]["name"], _group_2.name)
        self.assertEqual(response["results"][1]["name"], _group_3.name)

        response = self.client.get(
            self.url + "?channel_id=" + str(self.channel.id), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

        response = self.client.get(self.url + "?channel_id=" + str(_channel_3.id), **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 0)
