import os
import shutil
import uuid
from unittest import mock

from account.models import User
from authentication.keeps_permissions import ADMIN
from conftest import get_injector
from django.test import TestCase
from group.models import Group, GroupChannel, GroupMission, GroupUser
from group.services import GroupImportService
from group.services.group_import_service import ImportGroupUser
from mission.models import Mission
from model_mommy import mommy
from myaccount.application.services.myaccount_service import MyAccountService
from pulse.models import Channel


@mock.patch.object(MyAccountService, "has_access")
class GroupImportServiceTestCase(TestCase):
    fixtures = [
        "workspace",
        "user",
        "group",
        "mission_type",
        "mission_category",
        "mission",
        "channel_type",
        "channel_category",
        "channel",
    ]

    def setUp(self):
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace_2_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a30"

        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user = User.objects.get(id=self.user_id)
        self.user_2_id = "489fb596-478f-40b5-b717-4931f20e4cc6"

        self.group_id = "b5a88c6e-90ad-4155-9fc6-94ef7ae4271c"
        self.group = Group.objects.get(id=self.group_id)
        self.group_2_id = "b5a88c6e-90ad-4155-9fc6-94ef7ae4271a"
        self.group_3_id = "2de88d64-1b02-4c1f-94b1-76ad89f584ce"

        self.group_user_id = "1c1df74c-82d9-46a5-9db0-e1bfc4473081"

        self.mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"
        self.mission_2_id = "f1e43871-454b-4726-bbc4-eeb6e959381d"
        self.mission_3_id = "e68ec9ed-30ed-4f96-8e81-e782cd3b0f89"
        self.mission_4_id = "d48eac73-c363-4c1a-9683-ba46a6b97120"
        self.mission_5_id = "6d60a7df-0fb5-4551-915e-9f792b317ee5"

        self.mission1 = Mission.objects.get(id=self.mission_id)

        self.mission_workspace_id = "4328c2cf-a3de-4b18-92ed-481e95016613"
        self.mission_workspace_2_id = "d478a20e-9b17-404f-ab41-e0adcda31245"
        self.mission_workspace_3_id = "6d3b550f-2682-42aa-b69b-754a21c16351"
        self.mission_workspace_4_id = "89e8397a-78c2-4c4d-9db8-4e174ece8eed"
        self.mission_workspace_5_id = "32475124-64eb-4c45-b63e-5bf444183d09"

        mommy.make(GroupMission, mission_id=self.mission_4_id, group_id=self.group_2_id)
        mommy.make(GroupMission, mission_id=self.mission_5_id, group_id=self.group_id)

        self.channel_id = "40ae44f0-e023-4c1b-89fc-e31f78c7e8ca"
        self.channel_2_id = "eb5b1813-c3e2-4aff-809a-581d2d1d9476"
        self.channel_3_id = "6ef2ebaa-3a4e-462a-8803-b39c818d8d76"
        self.channel_4_id = "8e6a0b18-7b96-449b-990b-6b29cbbfd6a6"
        self.channel_5_id = "32956827-264f-4d80-8ab9-7f3707df214d"

        self.channel5 = Channel.objects.get(id=self.channel_5_id)

        mommy.make(GroupChannel, channel_id=self.channel_4_id, group_id=self.group_id)

        self.service = get_injector().get(GroupImportService)

    def test_import_user_that_not_exits(self, has_access):
        unknown_email = "<EMAIL>"
        import_group_users = [ImportGroupUser(user_email=unknown_email, group_name=self.group.name)]
        action_user = User(id=uuid.uuid4())
        action_user.role = ADMIN

        response = self.service.import_users(self.workspace_id, import_group_users, action_user)

        self.assertEqual(len(response["group_user_import_errors"]), 1)
        self.assertEqual(response["group_user_import_errors"][0]["user"], unknown_email)
        self.assertEqual(response["group_user_import_errors"][0]["message"], "user not found")

    def test_import_group_that_not_exits(self, *args):
        group_name = "New Group"
        import_group_users = [ImportGroupUser(user_email=self.user.email, group_name=group_name)]
        action_user = User(id=uuid.uuid4())
        action_user.role = ADMIN

        response = self.service.import_users(self.workspace_id, import_group_users, action_user)
        group_query = Group.objects.filter(workspace_id=self.workspace_id, name=group_name)

        self.assertEqual(len(response["group_user_import_errors"]), 0)
        self.assertTrue(group_query.exists())

    def test_import_user_already_linked_in_group(self, *args):
        mommy.make(GroupUser, user=self.user, group=self.group)
        import_group_users = [ImportGroupUser(user_email=self.user.email, group_name=self.group.name)]
        action_user = User(id=uuid.uuid4())
        action_user.role = ADMIN

        response = self.service.import_users(self.workspace_id, import_group_users, action_user)

        self.assertEqual(len(response["group_user_import_errors"]), 0)

    def test_group_mission_import(self, *args):
        """
        Import missions for groups

        Roles:
        - If mission not exist, don't link
        - If group not exist (for the workspace), the group will be create
        - If mission and group already linked, do nothing
        """
        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/group_mission_test.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)
        action_user = User(id=uuid.uuid4())
        action_user.role = ADMIN

        response = self.service.mission_group_from_xls(self.workspace_id, self.filename, action_user)
        self.assertEqual(Group.objects.filter(workspace_id=self.workspace_id).count(), 7)
        self.assertEqual(Group.objects.filter(workspace_id=self.workspace_2_id).count(), 3)

        # return two errors case
        self.assertEqual(len(response["group_mission_import_errors"]), 6)

        # case mission exist (name) for workspace 2 but not for workspace 1
        self.assertEqual(response["group_mission_import_errors"][0]["mission"], self.mission1.name)
        self.assertEqual(response["group_mission_import_errors"][0]["message"], "mission not found")

        # case mission not exist
        self.assertEqual(response["group_mission_import_errors"][1]["mission"], "Python Advanced")
        self.assertEqual(response["group_mission_import_errors"][1]["message"], "mission not found")

    def test_channel_mission_import(self, *args):
        """
        Import channels for groups

        Roles:
        - If channel not exist, don't link
        - If group not exist (for the workspace), the group will be create
        - If channel and group already linked, do nothing
        """
        self.template_sheet = os.path.dirname(os.path.dirname(__file__)) + "/tests/group_channel_test.xlsx"
        self.filename = os.path.dirname(os.path.dirname(__file__)) + "/tests/temp.xlsx"
        shutil.copyfile(self.template_sheet, self.filename)

        response = self.service.channel_group_from_xls(self.workspace_id, self.filename)
        self.assertEqual(Group.objects.filter(workspace_id=self.workspace_id).count(), 7)
        self.assertEqual(Group.objects.filter(workspace_id=self.workspace_2_id).count(), 3)

        # case group already exist for workspace
        self.assertEqual(response["success"][0]["channel"]["id"], self.channel_id)
        self.assertEqual(response["success"][0]["group"]["id"], self.group_id)

        # case group already exist (name) for workspace 2 but not workspace 1
        self.assertEqual(response["success"][1]["channel"]["id"], self.channel_2_id)
        self.assertEqual(response["success"][1]["group"]["name"], "Development")
        self.assertNotEqual(response["success"][1]["group"]["id"], self.group_2_id)

        # case group not exist
        self.assertEqual(response["success"][2]["channel"]["id"], self.channel_3_id)
        self.assertEqual(response["success"][2]["group"]["name"], "Customers")

        # case group and mission already linked
        self.assertEqual(response["success"][3]["channel"]["id"], self.channel_4_id)
        self.assertEqual(response["success"][3]["group"]["name"], "Partners")

        # case mission exist (name) for workspace 2 but not for workspace 1
        self.assertEqual(response["errors"][0]["channel"], self.channel5.name)
        self.assertEqual(response["errors"][0]["message"], "channel not found")
