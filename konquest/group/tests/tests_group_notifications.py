from unittest import mock

from django.test import TestCase
from group.models import GroupChannel, GroupLearningTrail, GroupMission, GroupUser
from group.tasks import notifications
from notification.services.notification_service import Message

MOCK_CREATE_NOTIFICATION = "notification.services.notification_service_v2.NotificationServiceV2.create_notification"


@mock.patch(MOCK_CREATE_NOTIFICATION)
class GroupNotificationsTestCase(TestCase):
    fixtures = [
        "base",
        "user",
        "workspace",
        "mission_category",
        "mission_type",
        "learning_trail_type",
        "channel_category",
        "channel_type",
        "mission",
        "learning_trail",
        "channel",
        "group",
        "group_user",
        "group_channel",
        "group_mission",
        "group_learning_trail",
    ]

    def setUp(self):
        self.group_user_id = "eefd645c-e4a3-4783-ba91-017ddb20c31b"
        self.group_channel_id = "eae57093-c1e5-4745-aa75-e448f4465751"
        self.group_mission_id = "6180814c-6976-4f5b-b594-8ba00a53d92c"
        self.group_trail_id = "e1ecf86c-eeee-4647-bc95-09a457d8642f"

    def test_notify_user_linked_in_a_new_group(self, mock_create_notification: mock.MagicMock):
        group_user = GroupUser.objects.get(id=self.group_user_id)
        notifications.notify_user_linked_in_a_new_group(group_user.id)
        message = Message(title="you_have_been_added_to_a_new_group", description=group_user.group.name)
        mock_create_notification.assert_called_once_with(
            user_ids=[group_user.user_id],
            type_key="USER_LINKED_IN_A_NEW_GROUP",
            action="CREATE",
            object=group_user.group_id,
            workspace_id=group_user.group.workspace_id,
            message=message,
        )

    def test_notify_new_channel_linked_in_group(self, mock_create_notification: mock.MagicMock):
        group_channel = GroupChannel.objects.get(id=self.group_channel_id)
        group_users = group_channel.group.groupuser_set.filter()
        notifications.notify_new_channel_linked_in_group(group_channel.id)
        message = Message(
            title="the_channel_%(channel_name)s_has_been_added_to_a_group",
            title_values={"channel_name": group_channel.channel.name},
            description=group_channel.group.name,
        )
        mock_create_notification.assert_called_with(
            user_ids=[group_user.user_id for group_user in group_users],
            type_key="NEW_CHANNEL_LINKED_IN_THE_GROUP",
            action="CREATE",
            object=group_channel.channel_id,
            workspace_id=group_channel.group.workspace_id,
            message=message,
        )

    def test_notify_new_mission_linked_in_group(self, mock_create_notification: mock.MagicMock):
        group_mission = GroupMission.objects.get(id=self.group_mission_id)
        group_users = group_mission.group.groupuser_set.filter()
        notifications.notify_new_mission_linked_in_group(group_mission.id)
        message = Message(
            title="the_mission_%(mission_name)s_has_been_added_to_a_group",
            title_values={"mission_name": group_mission.mission.name},
            description=group_mission.group.name,
        )
        mock_create_notification.assert_called_once_with(
            user_ids=[group_user.user_id for group_user in group_users],
            type_key="NEW_MISSION_LINKED_IN_THE_GROUP",
            action="CREATE",
            object=group_mission.mission_id,
            workspace_id=group_mission.group.workspace_id,
            message=message,
        )

    def test_notify_new_trail_linked_in_group(self, mock_create_notification: mock.MagicMock):
        group_trail = GroupLearningTrail.objects.get(id=self.group_trail_id)
        group_users = group_trail.group.groupuser_set.filter()
        notifications.notify_new_trail_linked_in_group(group_trail.id)
        message = Message(
            title="the_trail_%(trail_name)s_has_been_added_to_a_group",
            title_values={"trail_name": group_trail.learning_trail.name},
            description=group_trail.group.name,
        )
        mock_create_notification.assert_any_call(
            user_ids=[group_user.user_id for group_user in group_users],
            type_key="NEW_TRAIL_LINKED_IN_THE_GROUP",
            action="CREATE",
            object=group_trail.learning_trail_id,
            workspace_id=group_trail.group.workspace_id,
            message=message,
        )
