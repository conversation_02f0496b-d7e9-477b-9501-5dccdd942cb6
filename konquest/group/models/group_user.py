import uuid

from account.models import User
from django.db import models
from group.models.group import Group
from utils.models import BaseModel


class GroupUser(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    user = models.ForeignKey(User, verbose_name="User", on_delete=models.CASCADE)
    group = models.ForeignKey(Group, verbose_name="Group", on_delete=models.CASCADE)

    class Meta:
        verbose_name_plural = "Groups Users"
        unique_together = ("user", "group")
        db_table = "group_user"
