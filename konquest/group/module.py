from group.services import GroupFilterService, GroupImportService, GroupUserService
from group.services.group_learning_trail_service import GroupLearningTrailService
from group.services.group_mission_service import GroupMissionService
from group.services.group_service import GroupService
from injector import Module, inject, provider, singleton
from myaccount.application.services.myaccount_service import MyAccountService
from user_activity.services import LearningTrailBatchEnrollmentService, MissionBatchEnrollmentService


class GroupModule(Module):
    @inject
    @singleton
    @provider
    def group_learning_trail_service(
        self, learning_trail_batch_enrollment_service: LearningTrailBatchEnrollmentService
    ) -> GroupLearningTrailService:
        return GroupLearningTrailService(learning_trail_batch_enrollment_service)

    @inject
    @singleton
    @provider
    def group_user_service(self, myaccount_service: MyAccountService) -> GroupUserService:
        return GroupUserService(myaccount_service)

    @inject
    @provider
    def group_mission_service(
        self, mission_batch_enrollment_service: MissionBatchEnrollmentService
    ) -> GroupMissionService:
        return GroupMissionService(mission_batch_enrollment_service)

    @singleton
    @provider
    def group_filter_service(self) -> GroupFilterService:
        return GroupFilterService()

    @inject
    @provider
    def group_import_service(
        self,
        myaccount_service: MyAccountService,
        mission_batch_enrollment_service: MissionBatchEnrollmentService,
        group_user_service: GroupUserService,
        learning_trail_enrollment_service: LearningTrailBatchEnrollmentService,
    ) -> GroupImportService:
        return GroupImportService(
            mission_batch_enrollment_service=mission_batch_enrollment_service,
            trail_enrollment_service=learning_trail_enrollment_service,
            group_user_service=group_user_service,
            myaccount_service=myaccount_service,
        )

    @singleton
    @provider
    def group_service(self) -> GroupService:
        return GroupService()
