from typing import Sequence

from authentication.keeps_permissions import ADMIN_PERMISSIONS, MANAGED_PERMISSIONS
from custom.keeps_exception_handler import KeepsBadRequestError
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.utils import timezone
from django.utils.translation import gettext_noop
from django_filters.rest_framework import DjangoFilterBackend
from group.dataclasses.link_users_in_group_create import LinkUsersInGroupCreate
from group.models import Group, GroupMission, GroupUser
from group.serializers.group_import_serializer import GroupUserImportInputSerializer
from group.serializers.group_serializer import GroupUserImportSerializer
from group.serializers.group_user_serializer import (
    GroupUserCreateResponse,
    GroupUserDetailSerializer,
    GroupUserSerializer,
)
from group.services import GroupImportService, GroupUserService
from group.services.group_import_service import ImportGroupUser
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, <PERSON><PERSON>ilter
from rest_framework.response import Response
from user_activity.models import MissionEnrollment
from utils.utils import load_request_user, parser_excel, swagger_safe_queryset

SPREADSHEET_NOT_CONTAINS_USER_HEADER = gettext_noop("spreadsheet_not_contains_user_header")
SPREADSHEET_NOT_CONTAINS_GROUP_HEADER = gettext_noop("spreadsheet_not_contains_group_header")
GROUP_COLUMN = "group"
USER_COLUMN = "user"


class GroupUserUnitViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ["user__name", "user__email", "group__name"]
    filterset_fields = ("user__name", "user__email", "group__name")
    search_fields = ("user__name", "user__email", "group__name")
    ordering_fields = ("user__name", "user__email", "group__name")
    ordering = ("user__name",)

    permission_classes = ADMIN_PERMISSIONS

    def get_queryset(self):
        group_uuid = self.kwargs.get("group_uuid")
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid, id=group_uuid).values_list("id", flat=True)

        return GroupUser.objects.filter(group_id__in=workspace_groups).all()

    def get_serializer_class(self):
        return GroupUserSerializer

    def perform_destroy(self, instance):
        instance.update(deleted=True, deleted_date=timezone.now())

    def create(self, request, *args, **kwargs):
        request.data["group"] = self.kwargs.get("group_uuid")
        request.data["user"] = self.kwargs.get("user_uuid")

        exist = GroupUser.objects.filter(user_id=request.data["user"], group_id=request.data["group"]).exists()
        if exist:
            raise KeepsBadRequestError(
                detail="user is already registered in the group", i18n="user_group_already_exists"
            )

        serializer = self.get_serializer(data={"user": request.data["user"], "group": request.data["group"]})
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response(status=status.HTTP_201_CREATED)

    def destroy(self, request, *args, **kwargs):
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None
        group_id = self.kwargs.get("group_uuid")
        user_id = self.kwargs.get("user_uuid")

        delete_enrollment = self.request.query_params.get("delete_enrollment") == "True"

        workspace_has_group = (
            Group.objects.filter(workspace_id=workspace_uuid, id=group_id).values_list("id", flat=True).first()
        )

        if not workspace_has_group:
            return Response(status=status.HTTP_403_FORBIDDEN)

        user_object = GroupUser.objects.filter(group_id=group_id, user_id=user_id)

        if delete_enrollment:
            mission_group = GroupMission.objects.filter(group_id=group_id).values_list("mission_id", flat=True)
            enrollment_instance = MissionEnrollment.objects.filter(user_id=user_id, mission_id__in=mission_group).all()
            self.perform_destroy(enrollment_instance)

        if not user_object:
            return Response(status=status.HTTP_404_NOT_FOUND)

        self.perform_destroy(user_object)
        return Response(status=status.HTTP_204_NO_CONTENT)


class GroupUserViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("user__name", "user__email", "group__name", "deleted")
    search_fields = ("user__name", "user__email", "group__name")
    ordering_fields = ("user__name", "user__email", "group__name", "created_date")
    ordering = ("user__name",)

    permission_classes = ADMIN_PERMISSIONS

    @inject
    def __init__(self, group_user_service: GroupUserService = Provider[GroupUserService], **kwargs):
        super().__init__(**kwargs)
        self.group_user_service: GroupUserService = group_user_service

    @swagger_safe_queryset(GroupUser)
    def get_queryset(self):
        group_uuid = self.kwargs.get("group_uuid")
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid, id=group_uuid).values_list("id", flat=True)

        return GroupUser.objects.get_all_including_deleted().filter(group_id__in=workspace_groups)

    def get_serializer_class(self):
        return GroupUserSerializer if self.request.method in ["POST", "PUT", "PATCH"] else GroupUserDetailSerializer

    def create(self, request, *args, **kwargs):
        """
        create group_user

        Linked users in group and enroll that users in missions inside this group,
        if "enrollment_goal_date" is present in the body's root AND not None

        ---
            body:
                "users": [user_id_list],
                "enrollment_goal_date": optional, "YYYY-MM-DD HH:MM:SS",
                "enrollment_required_mission": optional, boll
        """
        workspace_id = self.request.user.get("client_id")
        group_id = self.kwargs.get("group_uuid")
        token = self.request.META.get("HTTP_AUTHORIZATION")

        serializer = GroupUserImportInputSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        user_id_list = data["users"]
        enrollment_goal_date = data.get("enrollment_goal_date")
        enrollment_required_mission = data["enrollment_required_mission"]
        regulatory_compliance_cycle_id = self.request.data.get("regulatory_compliance_cycle_id")

        response = GroupUserCreateResponse(
            self.group_user_service.link_user_in_group(
                LinkUsersInGroupCreate(
                    user_ids=list(set(user_id_list)),
                    group_id=group_id,
                    workspace_id=workspace_id,
                    token=token,
                    goal_date=enrollment_goal_date,
                    required=enrollment_required_mission,
                    regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
                )
            )
        )

        return Response(response.data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id")
        group_id = self.kwargs.get("group_uuid")
        group = Group.objects.filter(workspace_id=workspace_id, id=group_id).first()

        if not group:
            return Response(status=status.HTTP_403_FORBIDDEN)

        group_users = GroupUser.objects.get_all_including_deleted().filter(group=group, deleted=True)
        self.perform_destroy(group_users)

        return Response(status=status.HTTP_200_OK)


class GroupUserImportViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = MANAGED_PERMISSIONS
    serializer_class = GroupUserImportSerializer

    @inject
    def __init__(self, group_import_service: GroupImportService = Provider[GroupImportService], **kwargs):
        super().__init__(**kwargs)
        self.group_import_service = group_import_service

    def create(self, request, *args, **kwargs):
        workspace_uuid = self.request.user.get("client_id")
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        file = data["file"]

        enrollment_goal_date = data.get("enrollment_goal_date")
        enrollment_required_mission = data.get("enrollment_required_mission")

        import_group_users = self.extract_import_group_users_from_file(file)
        regulatory_compliance_cycle_id = self.request.data.get("regulatory_compliance_cycle")
        action_user = load_request_user(request)

        data_return = self.group_import_service.import_users(workspace_uuid, import_group_users, action_user,
                                                             enrollment_goal_date, enrollment_required_mission,
                                                             regulatory_compliance_cycle_id=regulatory_compliance_cycle_id)

        return Response(data_return, status=status.HTTP_200_OK)

    @staticmethod
    def extract_import_group_users_from_file(file: InMemoryUploadedFile) -> Sequence[ImportGroupUser]:
        raw = parser_excel(file)
        try:
            users = list(raw[USER_COLUMN].values())
        except KeyError:
            raise KeepsBadRequestError(i18n=SPREADSHEET_NOT_CONTAINS_USER_HEADER)
        try:
            groups = list(raw[GROUP_COLUMN].values())
        except KeyError:
            raise KeepsBadRequestError(i18n=SPREADSHEET_NOT_CONTAINS_GROUP_HEADER)

        import_group_users = []
        for index, user in enumerate(users):
            import_group_users.append(ImportGroupUser(user_email=user.strip(), group_name=groups[index]))
        return import_group_users
