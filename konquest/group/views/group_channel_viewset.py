# -*- coding: utf-8 -*-
from authentication.keeps_permissions import ADMIN_PERMISSIONS
from django_filters.rest_framework import DjangoFilterBackend
from group.models import Group, GroupChannel
from group.serializers.group_channel_serializer import GroupChannelDetailSerializer, GroupChannelSerializer
from group.services import GroupImportService
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response
from utils.utils import storage_file, swagger_safe_queryset


class GroupChannelUnitViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ["channel__name", "group__name"]
    filterset_fields = ("channel", "group", "channel__name", "group__name")
    search_fields = ("channel", "group", "channel__name", "group__name")
    ordering_fields = ("channel__name", "group__name")
    ordering = ("channel__name",)

    permission_classes = ADMIN_PERMISSIONS

    def get_queryset(self):
        group_uuid = self.kwargs.get("group_uuid")
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid, id=group_uuid).values_list("id", flat=True)

        return GroupChannel.objects.filter(group_id__in=workspace_groups).all()

    def get_serializer_class(self):
        return (
            GroupChannelSerializer if self.request.method in ["POST", "PUT", "PATCH"] else GroupChannelDetailSerializer
        )

    def create(self, request, *args, **kwargs):
        request.data["group"] = self.kwargs.get("group_uuid")
        request.data["channel"] = self.kwargs.get("mission_uuid")

        response = super(GroupChannelUnitViewSet, self).create(request, *args, **kwargs)
        return response

    def destroy(self, request, *args, **kwargs):
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None
        group = self.kwargs.get("group_uuid")
        channel = self.kwargs.get("mission_uuid")

        workspace_has_group = (
            Group.objects.filter(workspace_id=workspace_uuid, id=group).values_list("id", flat=True).first()
        )

        if not workspace_has_group:
            return Response(status=status.HTTP_403_FORBIDDEN)

        instance = GroupChannel.objects.filter(group_id=group, channel_id=channel)

        if not instance:
            return Response(status=status.HTTP_404_NOT_FOUND)

        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)


class GroupChannelViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ["channel__name", "group__name"]
    filterset_fields = ("channel", "group", "channel__name", "group__name")
    search_fields = ("channel__name", "group__name")
    ordering_fields = ("channel__name", "group__name")
    ordering = ("channel__name",)

    permission_classes = ADMIN_PERMISSIONS

    @swagger_safe_queryset(GroupChannel)
    def get_queryset(self):
        group_uuid = self.kwargs.get("group_uuid")
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid, id=group_uuid).values_list("id", flat=True)

        return GroupChannel.objects.filter(group_id__in=workspace_groups)

    def get_serializer_class(self):
        return (
            GroupChannelSerializer if self.request.method in ["POST", "PUT", "PATCH"] else GroupChannelDetailSerializer
        )

    def create(self, request, *args, **kwargs):
        group = self.kwargs.get("group_uuid")
        missions = request.data

        data_return = {"success": [], "errors": []}

        for channel in missions:
            try:
                serializer = self.get_serializer(data={"channel": channel, "group": group})
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)
                data_return["success"].append(serializer.data)

            except Exception as e:
                data_return["errors"].append({"channel": channel, "error": e.detail})

        return Response(data_return, status=status.HTTP_200_OK)


class GroupChannelImportViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ADMIN_PERMISSIONS

    @inject
    def __init__(self, service: GroupImportService = Provider[GroupImportService], **kwargs):
        super().__init__(**kwargs)
        self.group_import_service = service

    def create(self, request, *args, **kwargs):
        workspace_uuid = self.request.user.get("client_id")

        if "file" in request.data:
            file = storage_file(request.data["file"])
            data_return = self.group_import_service.channel_group_from_xls(workspace_uuid, file)
            return Response(data_return, status=status.HTTP_200_OK)

        else:
            return Response("file not found", status=status.HTTP_400_BAD_REQUEST)
