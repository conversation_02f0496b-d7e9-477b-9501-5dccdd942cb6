import os
from dataclasses import dataclass
from datetime import date
from typing import List, Sequence

import xlrd
from account.models import User
from group.models import Group, GroupChannel, GroupLearningTrail, GroupMission, GroupUser
from group.services import GroupUserService
from mission.models import Mission, MissionWorkspace
from myaccount.application.services.myaccount_service import MyAccountService
from myaccount.domain.repositories.myaccount_respository import UserHasPermissionDTO
from pulse.models import Channel
from user_activity.services import MissionBatchEnrollmentService
from user_activity.services.learning_trail_batch_enrollment_service import LearningTrailBatchEnrollmentService
from user_activity.services.learning_trail_batch_enrollment_service_v2 import EnrollmentsBatch
from user_activity.services.mission_batch_enrollment_service import EnrollmentsBatch as MissionEnrollmentsBatch

USER_NOT_FOUND = "user not found"
SUCCESS = "success"
ERRORS = "errors"


@dataclass
class ImportGroupUser:
    user_email: str
    group_name: str


class GroupImportService:
    def __init__(
        self,
        myaccount_service: MyAccountService,
        mission_batch_enrollment_service: MissionBatchEnrollmentService,
        group_user_service: GroupUserService,
        trail_enrollment_service: LearningTrailBatchEnrollmentService,
    ):
        self._mission_batch_enrollment_service: MissionBatchEnrollmentService = mission_batch_enrollment_service
        self._trail_enrollment_service: LearningTrailBatchEnrollmentService = trail_enrollment_service
        self._group_user_service = group_user_service
        self._myaccount_service = myaccount_service

    def import_users(
        self,
        workspace_id: str,
        import_group_users: Sequence[ImportGroupUser],
        action_user: User,
        goal_date: date = None,
        required: bool = None,
        **kwargs,
    ) -> dict:
        group_user_import_result = self._process_import_group_users(workspace_id, import_group_users)
        errors = {"group_user_import_errors": group_user_import_result[ERRORS]}
        if not goal_date:
            return errors

        enrollment_result = self.make_enrollment_by_users(
            group_user_list=group_user_import_result[SUCCESS],
            workspace_id=workspace_id,
            goal_date=goal_date,
            required=required,
            action_user=action_user,
            regulatory_compliance_cycle_id=kwargs.get("regulatory_compliance_cycle_id"),
        )
        errors.update(enrollment_result)
        return errors

    def _process_import_group_users(self, workspace_id: str, import_group_users: Sequence[ImportGroupUser]) -> dict:
        data_return = {SUCCESS: [], ERRORS: []}

        for group in import_group_users:
            email = group.user_email
            user, message = self._verify_user(email, workspace_id)
            if not user:
                data_return[ERRORS].append({"user": email, "message": message})
                continue
            group, _ = Group.objects.get_or_create(name=group.group_name, workspace_id=workspace_id)
            group_user, _ = GroupUser.objects.get_all_including_deleted().get_or_create(user=user, group=group)
            group_user.update(deleted=False, deleted_date=None)
            data_return[SUCCESS].append(self._get_success_detail(group_user))

        return data_return

    def _verify_user(self, email: str, workspace_id: str):
        user = User.objects.filter(email=email).first()
        if not user:
            return None, USER_NOT_FOUND
        user_has_role_in_workspace = self._myaccount_service.has_access(
            UserHasPermissionDTO(workspace_id=workspace_id, user_id=user.id)
        )
        if not user_has_role_in_workspace:
            return None, USER_NOT_FOUND
        return user, "user found"

    @staticmethod
    def _get_success_detail(group_user: GroupUser) -> dict:
        return {
            "id": str(group_user.id),
            "user": {
                "id": str(group_user.user.id),
                "name": group_user.user.name,
                "email": group_user.user.email,
            },
            "group": {"id": str(group_user.group.id), "name": group_user.group.name},
        }

    def make_enrollment_by_users(self, group_user_list, workspace_id, goal_date, required, action_user: User, **kwargs):
        if not group_user_list or goal_date is None:
            return {}

        enrollment_errors = []
        for data in group_user_list:
            group = data["group"]["id"]
            user = data["user"]["id"]

            group_missions = GroupMission.objects.filter(group_id=str(group)).all().values_list("mission_id", flat=True)
            trail_ids = GroupLearningTrail.objects.filter(group_id=str(group)).values_list(
                "learning_trail_id", flat=True
            )
            regulatory_compliance_cycle_id = kwargs.get("regulatory_compliance_cycle_id")
            batch = MissionEnrollmentsBatch(
                mission_ids=group_missions,
                user_ids=[user],
                workspace_id=workspace_id,
                action_user=action_user,
                goal_date=goal_date,
                required=required,
                regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            )
            enrollment_result = self._mission_batch_enrollment_service.enroll(batch)
            self._trail_enrollment_service.enroll(
                EnrollmentsBatch(
                    trail_ids=trail_ids,
                    workspace_id=workspace_id,
                    user_ids=[user],
                    goal_date=goal_date,
                    action_user=action_user,
                    regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
                )
            )
            enrollment_result = enrollment_result["enrollment_errors"]

            enrollment_errors.extend(enrollment_result)

        return {"enrollment_errors": enrollment_errors}

    def mission_group_from_xls(self, workspace_id: str, file: str, action_user: User, **kwargs):
        data = self.xls_parse(file, ["group", "mission"])
        group_mission_import_result = self.mission_group(workspace_id, data)
        goal_date = kwargs.get("goal_date")
        required = kwargs.get("required")
        regulatory_compliance_cycle_id = kwargs.get("regulatory_compliance_cycle_id")

        return_errors = {"group_mission_import_errors": group_mission_import_result[ERRORS]}
        if goal_date is not None:
            self.make_enrollment_by_mission(
                group_mission_list=group_mission_import_result[SUCCESS],
                workspace_id=workspace_id,
                action_user=action_user,
                goal_date=goal_date,
                required=required,
                regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            )

        return return_errors

    @staticmethod
    def mission_group(workspace_id, data):
        data_return = {SUCCESS: [], ERRORS: []}

        for _data in data:
            group, _ = Group.objects.get_or_create(name=_data["group"], workspace_id=workspace_id)
            workspace_missions = MissionWorkspace.objects.filter(workspace_id=workspace_id).values_list(
                "mission_id", flat=True
            )
            mission = Mission.objects.filter(id__in=workspace_missions, name=_data["mission"]).first()

            if mission:
                instance, _ = GroupMission.objects.get_or_create(mission=mission, group=group)

                data_return[SUCCESS].append(
                    {
                        "id": str(instance.id),
                        "mission": {"id": str(instance.mission.id), "name": instance.mission.name},
                        "group": {"id": str(group.id), "name": group.name},
                    }
                )

            else:
                data_return[ERRORS].append({"mission": _data["mission"], "message": "mission not found"})

        return data_return

    def make_enrollment_by_mission(
        self,
        group_mission_list: List[dict],
        workspace_id: str,
        action_user: User,
        goal_date: str,
        required: bool,
        regulatory_compliance_cycle_id: str,
    ):
        """
        Function to make enrollment of the user, after new missions linked in many groups

        :param group_mission_list: [{"group": {"id": group_id}, "mission": {"id":mission_id}}],
        :param workspace_id: str, workspace id of the groups
        :param action_user: User, action user
        :param goal_date: "YYYY-MM-DD HH:MM:SS",
        :param required: if True, the mission enrollments will expire after the enrollment_goal_date
        :param regulatory_compliance_cycle_id: regulatory_compliance_cycle_id

        :return a dict {"enrollment_errors": []}
        """

        if not group_mission_list or goal_date is None:
            return {}

        enrollment_errors = []
        for data in group_mission_list:
            group = data["group"]["id"]
            mission = data["mission"]["id"]

            users = GroupUser.objects.filter(group_id=str(group)).all().values_list("user_id", flat=True)

            batch = MissionEnrollmentsBatch(
                mission_ids=[mission],
                user_ids=users,
                workspace_id=workspace_id,
                action_user=action_user,
                goal_date=goal_date,
                required=required,
                regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            )
            enrollment_result = self._mission_batch_enrollment_service.enroll(batch)
            enrollment_result = enrollment_result["enrollment_errors"]

            enrollment_errors.extend(enrollment_result)

        return {"enrollment_errors": enrollment_errors}

    def channel_group_from_xls(self, workspace_id, file):
        data = self.xls_parse(file, ["group", "channel"])
        return self.channel_group(workspace_id, data)

    @staticmethod
    def channel_group(workspace_id, data):
        output = {SUCCESS: [], ERRORS: []}

        for _data in data:
            group, _ = Group.objects.get_or_create(name=_data["group"], workspace_id=workspace_id)
            channel = Channel.objects.filter(workspace_id=workspace_id, name=_data["channel"]).first()

            if channel:
                instance, _ = GroupChannel.objects.get_or_create(channel=channel, group=group)

                output[SUCCESS].append(
                    {
                        "id": str(instance.id),
                        "channel": {"id": str(instance.channel.id), "name": instance.channel.name},
                        "group": {"id": str(group.id), "name": group.name},
                    }
                )

            else:
                output[ERRORS].append({"channel": _data["channel"], "message": "channel not found"})

        return output

    def xls_parse(self, file, keys):
        data = []

        for column in self.xls_read(file):
            data_parser = dict()
            data_parser[keys[0]] = column[0].strip()
            data_parser[keys[1]] = column[1].strip()
            data.append(data_parser)

        os.remove(file)

        return data

    @staticmethod
    def xls_read(arq_xls):
        """
        read
        """

        xls = xlrd.open_workbook(arq_xls)
        plan = xls.sheets()[0]
        for i in range(1, plan.nrows):
            yield plan.row_values(i)
