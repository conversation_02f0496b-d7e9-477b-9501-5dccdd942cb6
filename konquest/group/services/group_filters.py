from group.models import Group, GroupChannel, GroupLearningTrail, GroupMission, GroupUser


class GroupFilterService:
    def __init__(self):
        pass

    @staticmethod
    def mission_filter(workspace_id, user_id):
        """
        all missions allowed for workspace and user

        :return: missions (id)
        """
        groups_workspace = Group.objects.filter(workspace_id=workspace_id).values_list("id", flat=True)
        groups_user = GroupUser.objects.filter(user_id=user_id, group_id__in=groups_workspace).values_list(
            "group_id", flat=True
        )
        missions_id = GroupMission.objects.filter(group_id__in=groups_user).values_list("mission_id", flat=True)

        return missions_id

    @staticmethod
    def channel_filter(workspace_id, user_id):
        """
        all missions allowed for workspace and user

        :return: channels (id)
        """
        groups_workspace = Group.objects.filter(workspace_id=workspace_id).values_list("id", flat=True)
        groups_user = GroupUser.objects.filter(user_id=user_id, group_id__in=groups_workspace).values_list(
            "group_id", flat=True
        )
        channel_id = GroupChannel.objects.filter(group_id__in=groups_user).values_list("channel_id", flat=True)

        return channel_id

    @staticmethod
    def learning_trail_filter(workspace_id, user_id) -> list:
        """
        all learning trails allowed for workspace and user

        :return: learning trail ids (id)
        """
        groups_workspace = Group.objects.filter(workspace_id=workspace_id).values_list("id", flat=True)
        groups_user = GroupUser.objects.filter(user_id=user_id, group_id__in=groups_workspace).values_list(
            "group_id", flat=True
        )
        lts_id = GroupLearningTrail.objects.filter(group_id__in=groups_user).values_list("learning_trail_id", flat=True)

        return lts_id
