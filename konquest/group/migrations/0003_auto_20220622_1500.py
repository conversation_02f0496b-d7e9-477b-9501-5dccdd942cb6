# Generated by Django 2.2 on 2022-06-22 15:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("account", "0002_auto_20220622_1500"),
        ("group", "0002_auto_20220417_1927"),
    ]

    operations = [
        migrations.RenameField(
            model_name="group",
            old_name="company",
            new_name="workspace",
        ),
        migrations.AlterField(
            model_name="group",
            name="workspace",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="account.Workspace",
                verbose_name="Workspace",
            ),
        ),
        migrations.AlterField(
            model_name="group",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterField(
            model_name="groupchannel",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="grouplearningtrail",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterField(
            model_name="groupmission",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterField(
            model_name="groupuser",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name="Created Date"),
        ),
        migrations.AlterUniqueTogether(
            name="group",
            unique_together={("workspace", "name")},
        ),
    ]
