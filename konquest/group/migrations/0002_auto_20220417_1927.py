# Generated by Django 2.2 on 2022-04-17 19:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("learning_trail", "0001_initial"),
        ("pulse", "0001_initial"),
        ("mission", "0001_initial"),
        ("account", "0001_initial"),
        ("group", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="groupmission",
            name="mission",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="mission.Mission", verbose_name="Mission"
            ),
        ),
        migrations.AddField(
            model_name="grouplearningtrail",
            name="group",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="group.Group", verbose_name="Group"
            ),
        ),
        migrations.AddField(
            model_name="grouplearningtrail",
            name="learning_trail",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="learning_trail.LearningTrail",
                verbose_name="LearningTrail",
            ),
        ),
        migrations.AddField(
            model_name="groupchannel",
            name="channel",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="pulse.Channel", verbose_name="Channel"
            ),
        ),
        migrations.AddField(
            model_name="groupchannel",
            name="group",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="group.Group", verbose_name="Group"
            ),
        ),
        migrations.AddField(
            model_name="group",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="account.Company", verbose_name="Company"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="groupuser",
            unique_together={("user", "group")},
        ),
        migrations.AlterUniqueTogether(
            name="groupmission",
            unique_together={("mission", "group")},
        ),
        migrations.AlterUniqueTogether(
            name="grouplearningtrail",
            unique_together={("learning_trail", "group")},
        ),
        migrations.AlterUniqueTogether(
            name="groupchannel",
            unique_together={("channel", "group")},
        ),
        migrations.AlterUniqueTogether(
            name="group",
            unique_together={("company", "name")},
        ),
    ]
