# Generated by Django 2.2 on 2022-04-17 19:27

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("account", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Group",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=200, verbose_name="Name")),
                ("description", models.TextField(blank=True, null=True, verbose_name="Description")),
            ],
            options={
                "verbose_name_plural": "Groups",
                "db_table": "group",
            },
        ),
        migrations.CreateModel(
            name="GroupChannel",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
            ],
            options={
                "verbose_name_plural": "Channels Groups",
                "db_table": "group_channel",
            },
        ),
        migrations.CreateModel(
            name="GroupLearningTrail",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
            ],
            options={
                "verbose_name_plural": "Learning Trails Groups",
                "db_table": "group_learning_trail",
            },
        ),
        migrations.CreateModel(
            name="GroupUser",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="group.Group", verbose_name="Group"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="account.User", verbose_name="User"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Groups Users",
                "db_table": "group_user",
            },
        ),
        migrations.CreateModel(
            name="GroupMission",
            fields=[
                ("created_date", models.DateTimeField(auto_now_add=True, verbose_name="Created Date")),
                ("updated_date", models.DateTimeField(auto_now=True, verbose_name="Updated Date")),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="group.Group", verbose_name="Group"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Missions Groups",
                "db_table": "group_mission",
            },
        ),
    ]
