# Generated by Django 2.2 on 2022-09-08 12:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("group", "0002_auto_20220417_1927"),
    ]

    operations = [
        migrations.AddField(
            model_name="group",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="group",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="groupchannel",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="groupchannel",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="grouplearningtrail",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="grouplearningtrail",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="groupmission",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="groupmission",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
        migrations.AddField(
            model_name="groupuser",
            name="deleted",
            field=models.BooleanField(default=False, verbose_name="Deleted"),
        ),
        migrations.AddField(
            model_name="groupuser",
            name="deleted_date",
            field=models.DateTimeField(null=True, verbose_name="Deleted Date"),
        ),
    ]
