# -*- coding: utf-8 -*-

from account.serializers.user_serializer import UserSerializer
from group.dataclasses.link_users_in_group_result import LinkUsersInGroupResult
from group.models.group_user import GroupUser
from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer


class GroupUserDetailSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = GroupUser
        fields = "__all__"


class GroupUserSerializer(serializers.ModelSerializer):
    regulatory_compliance_cycle_id = serializers.UUIDField(required=False)

    class Meta:
        model = GroupUser
        fields = "__all__"


class GroupUserCreateResponse(DataclassSerializer):
    class Meta:
        dataclass = LinkUsersInGroupResult
