# -*- coding: utf-8 -*-

from group.models import GroupLearningTrail
from learning_trail.serializers.learning_trail_serializer import LearningTrailSerializer
from rest_framework import serializers


class GroupLearningTrailDetailSerializer(serializers.ModelSerializer):
    learning_trail = LearningTrailSerializer()

    class Meta:
        model = GroupLearningTrail
        fields = "__all__"


class GroupLearningTrailSerializer(serializers.ModelSerializer):
    regulatory_compliance_cycle_id = serializers.CharField(required=False)

    class Meta:
        model = GroupLearningTrail
        fields = "__all__"
