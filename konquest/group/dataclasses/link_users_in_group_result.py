from dataclasses import dataclass
from typing import Optional, Sequence


@dataclass
class ErrorDetail:
    i18n: str
    detail: str


@dataclass
class UserDetail:
    id: str
    email: Optional[str] = None


@dataclass
class GroupDetail:
    id: str
    name: Optional[str] = None


@dataclass
class GroupUserErrors:
    user: UserDetail
    group: GroupDetail
    error: ErrorDetail


@dataclass
class Warning:
    object: str
    warning_detail: ErrorDetail


@dataclass
class LinkUsersInGroupResult:
    group_user_errors: Sequence[GroupUserErrors]
    group_user_warnings: Sequence[Warning]
