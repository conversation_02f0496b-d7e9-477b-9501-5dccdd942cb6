from celery import shared_task
from config import settings
from group.services.group_user_service import GroupUserService
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def disabled_group_user_by_workspace(user_id: str, workspace_id: str):
    with task_transaction("enable_group_user_by_workspace", GroupUserService) as service:
        service.disable_groups_user_workspace(user_id, workspace_id)
