from account.models import User
from account.models.workspace import Workspace
from django.test import TestCase
from group.models import Group, GroupUser
from model_mommy import mommy

from .disabled_group_user_by_workspace import disabled_group_user_by_workspace
from .enable_group_user_by_workspace import enable_group_user_by_workspace


class TestDisabledEnabledGroupUserByWorkspace(TestCase):
    def setUp(self):
        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.workspace2 = mommy.make(Workspace)
        self.group = mommy.make(Group, workspace=self.workspace)
        self.group_user = mommy.make(GroupUser, user=self.user, group=self.group)
        self.group2 = mommy.make(Group, workspace=self.workspace2)
        self.group_user2 = mommy.make(GroupUser, user=self.user, group=self.group2)

    def test_disable_group_user_workspace(self):
        self.assertEqual(GroupUser.objects.filter(user=self.user, group=self.group).count(), 1)
        disabled_group_user_by_workspace(self.user.id, self.workspace.id)
        self.assertEqual(GroupUser.objects.filter(user=self.user, group=self.group).count(), 0)
        self.assertEqual(GroupUser.objects.filter(user=self.user, group=self.group2).count(), 1)

    def test_enable_group_user_workspace(self):
        self.assertEqual(GroupUser.objects.filter(user=self.user, group=self.group).count(), 1)
        disabled_group_user_by_workspace(self.user.id, self.workspace.id)
        self.assertEqual(GroupUser.objects.filter(user=self.user, group=self.group).count(), 0)
        enable_group_user_by_workspace(self.user.id, self.workspace.id)
        self.assertEqual(GroupUser.objects.filter(user=self.user, group=self.group).count(), 1)
        self.assertEqual(GroupUser.objects.all().count(), 2)
