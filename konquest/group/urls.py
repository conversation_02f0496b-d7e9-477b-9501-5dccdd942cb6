# -*- coding: utf-8 -*-

from django.urls import path
from group.views.group_channel_viewset import GroupChannelImportViewSet, GroupChannelUnitViewSet, GroupChannelViewSet
from group.views.group_learning_trail_viewset import GroupLearningTrailUnitViewSet, GroupLearningTrailViewSet
from group.views.group_mission_viewset import GroupMissionImportViewSet, GroupMissionUnitViewSet, GroupMissionViewSet
from group.views.group_user_viewset import GroupUserImportViewSet, GroupUserUnitViewSet, GroupUserViewSet
from group.views.group_viewset import GroupViewSet

_read_only = {"get": "list"}

_create_only = {"post": "create"}

_read_only_detail = {"get": "retrieve"}

_delete_only = {"delete": "destroy"}

_list = {"get": "list", "post": "create"}

_detail = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}

_create_delete = {"post": "create", "delete": "destroy"}

urlpatterns = [
    path("", GroupViewSet.as_view(_list), name="groups-list"),
    path("/<uuid:pk>", GroupViewSet.as_view(_detail), name="groups-detail"),
    path(
        "/<uuid:group_uuid>/delete-unabled-users",
        GroupUserViewSet.as_view(_delete_only),
        name="delete-unabled-group-user",
    ),
    path("/<uuid:group_uuid>/users", GroupUserViewSet.as_view(_list), name="group-users"),
    path("/<uuid:group_uuid>/users/<uuid:user_uuid>", GroupUserUnitViewSet.as_view(_create_delete), name="group-user"),
    path("/<uuid:group_uuid>/missions", GroupMissionViewSet.as_view(_list), name="group-missions"),
    path(
        "/<uuid:group_uuid>/missions/<uuid:mission_uuid>",
        GroupMissionUnitViewSet.as_view(_create_delete),
        name="group-mission",
    ),
    path("/<uuid:group_uuid>/learning-trails", GroupLearningTrailViewSet.as_view(_list), name="group-learning-trails"),
    path(
        "/<uuid:group_uuid>/learning-trails/<uuid:learning_trail_uuid>",
        GroupLearningTrailUnitViewSet.as_view(_create_delete),
        name="group-learning-trail",
    ),
    path("/<uuid:group_uuid>/channels", GroupChannelViewSet.as_view(_list), name="group-channels"),
    path(
        "/<uuid:group_uuid>/channels/<uuid:mission_uuid>",
        GroupChannelUnitViewSet.as_view(_create_delete),
        name="group-channel",
    ),
    path("/users/import", GroupUserImportViewSet.as_view(_create_only), name="group-users-import"),
    path("/missions/import", GroupMissionImportViewSet.as_view(_create_only), name="group-missions-import"),
    path("/channels/import", GroupChannelImportViewSet.as_view(_create_only), name="group-channels-import"),
]
